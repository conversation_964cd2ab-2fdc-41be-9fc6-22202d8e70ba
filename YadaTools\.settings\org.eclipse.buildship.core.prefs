arguments=--init-script C\:\\Users\\xtian\\AppData\\Roaming\\Windsurf\\User\\globalStorage\\redhat.java\\1.44.0\\config_win\\org.eclipse.osgi\\58\\0\\.cp\\gradle\\init\\init.gradle --init-script C\:\\Users\\xtian\\AppData\\Roaming\\Windsurf\\User\\globalStorage\\redhat.java\\1.44.0\\config_win\\org.eclipse.osgi\\58\\0\\.cp\\gradle\\protobuf\\init.gradle
auto.sync=true
build.scans.enabled=false
connection.gradle.distribution=GRADLE_DISTRIBUTION(LOCAL_INSTALLATION(C\:\\Users\\xtian\\AppData\\Roaming\\Windsurf\\User\\globalStorage\\pleiades.java-extension-pack-jdk\\gradle\\latest))
connection.project.dir=
eclipse.preferences.version=1
gradle.user.home=
java.home=C\:/local/jdk17
jvm.arguments=
offline.mode=false
override.workspace.settings=true
show.console.view=true
show.executions.view=true
