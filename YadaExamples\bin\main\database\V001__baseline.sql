create table YadaAddress (id bigint not null auto_increment, modified DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, number varchar(8), owner_id bigint, version bigint not null, zipCode varchar(16), city varchar(64), country varchar(64), notes varchar(64), state varchar(64), street varchar(64), primary key (id)) engine=InnoDB;
create table YadaArticle (depth float(23), diameter float(23), elements integer, height float(23), length float(23), published bit not null, radius float(23), weight float(23), width float(23), id bigint not null auto_increment, image_id bigint, modified DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, product_id bigint, unitPrice bigint, version bigint not null, size varchar(16), sku varchar(32), internalName varchar(255), primary key (id)) engine=InnoDB;
create table YadaArticle_attachments (YadaArticle_id bigint not null, attachments_id bigint not null) engine=InnoDB;
create table YadaArticle_color (YadaArticle_id bigint not null, color varchar(32), locale varchar(32) not null, primary key (YadaArticle_id, locale)) engine=InnoDB;
create table YadaArticle_galleryImages (YadaArticle_id bigint not null, galleryImages_id bigint not null) engine=InnoDB;
create table YadaArticle_name (YadaArticle_id bigint not null, locale varchar(32) not null, name varchar(256), primary key (YadaArticle_id, locale)) engine=InnoDB;
create table YadaArticle_silhouetteImages (YadaArticle_id bigint not null, silhouetteImages_id bigint not null) engine=InnoDB;
create table YadaAttachedFile (height integer not null, heightDesktop integer, heightMobile integer, heightPdf integer, published bit not null, width integer not null, widthDesktop integer, widthMobile integer, widthPdf integer, id bigint not null auto_increment, modified DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, sortOrder bigint not null, uploadTimestamp TIMESTAMP NULL null, version bigint not null, metadata varchar(1024), clientFilename varchar(255), filename varchar(255), filenameDesktop varchar(255), filenameMobile varchar(255), filenamePdf varchar(255), forLocale varchar(255), relativeFolderPath varchar(255), primary key (id)) engine=InnoDB;
create table YadaAttachedFile_description (YadaAttachedFile_id bigint not null, locale varchar(32) not null, description varchar(8192), primary key (YadaAttachedFile_id, locale)) engine=InnoDB;
create table YadaAttachedFile_title (YadaAttachedFile_id bigint not null, locale varchar(32) not null, title varchar(1024), primary key (YadaAttachedFile_id, locale)) engine=InnoDB;
create table YadaAutoLoginToken (expiration TIMESTAMP NULL null, id bigint not null auto_increment, timestamp TIMESTAMP NULL null, token bigint not null, version bigint not null, yadaUserCredentials_id bigint, primary key (id)) engine=InnoDB;
create table YadaBrowserId (id bigint not null auto_increment, leastSigBits bigint, mostSigBits bigint, version bigint not null, primary key (id)) engine=InnoDB;
create table YadaCart (creationTimestamp TIMESTAMP NULL null, id bigint not null auto_increment, modified DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, owner_id bigint, version bigint not null, primary key (id)) engine=InnoDB;
create table YadaCartItem (quantity integer not null, article_id bigint, cart_id bigint, id bigint not null auto_increment, modified DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, version bigint not null, primary key (id)) engine=InnoDB;
create table YadaClause (clauseVersion integer not null, id bigint not null auto_increment, version bigint not null, name varchar(32) not null, content tinytext, primary key (id)) engine=InnoDB;
create table YadaCommentMessage (totReplies integer not null, id bigint not null, repliesTo_id bigint, totLikes bigint not null, primary key (id)) engine=InnoDB;
create table YadaCommentMessageLiked (YadaCommentMessage_id bigint not null, YadaUserProfile_id bigint not null, primary key (YadaCommentMessage_id, YadaUserProfile_id)) engine=InnoDB;
create table YadaCommerceArticle (availableQuantity integer not null, daysBeforeAvailable integer not null, id bigint not null, primary key (id)) engine=InnoDB;
create table YadaJob (errorStreakCount integer not null, jobGroupPaused bit not null, jobPriority integer not null, jobRecoverable bit not null, id bigint not null auto_increment, jobLastSuccessfulRun TIMESTAMP NULL null, jobScheduledTime TIMESTAMP NULL null, jobStartTime TIMESTAMP NULL null, jobStateObject_id bigint, jobGroup varchar(128), jobName varchar(128), jobDescription varchar(256), primary key (id)) engine=InnoDB;
create table YadaJob_BeActive (YadaJob_id bigint not null, jobsMustBeActive_id bigint not null) engine=InnoDB;
create table YadaJob_BeCompleted (YadaJob_id bigint not null, jobsMustComplete_id bigint not null) engine=InnoDB;
create table YadaJob_BeInactive (YadaJob_id bigint not null, jobsMustBeInactive_id bigint not null) engine=InnoDB;
create table YadaOrder (currency varchar(4), creationTimestamp TIMESTAMP NULL null, id bigint not null auto_increment, modified DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, orderStatus_id bigint, owner_id bigint, shippingTimestamp TIMESTAMP NULL null, stateChangeTimestamp TIMESTAMP NULL null, totalPrice bigint, version bigint not null, trackingData varchar(512), notes varchar(2048), primary key (id)) engine=InnoDB;
create table YadaOrderItem (quantity integer not null, id bigint not null auto_increment, modified DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, order_id bigint not null, unitPrice bigint, version bigint not null, articleCode varchar(255), primary key (id)) engine=InnoDB;
create table YadaPersistentEnum (enumOrdinal integer not null, id bigint not null auto_increment, enumClassName varchar(191) not null, enumName varchar(255) not null, primary key (id)) engine=InnoDB;
create table YadaPersistentEnum_langToText (YadaPersistentEnum_id bigint not null, language varchar(32) not null, localText varchar(128), primary key (YadaPersistentEnum_id, language)) engine=InnoDB;
create table YadaProduct (accessoryFlag bit not null, published bit not null, year integer not null, id bigint not null auto_increment, image_id bigint, modified DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, version bigint not null, primary key (id)) engine=InnoDB;
create table YadaProduct_accessories (accessories_id bigint not null, accessoryOf_id bigint not null) engine=InnoDB;
create table YadaProduct_attachments (YadaProduct_id bigint not null, attachments_id bigint not null) engine=InnoDB;
create table YadaProduct_categories (YadaProduct_id bigint not null, categories_id bigint not null) engine=InnoDB;
create table YadaProduct_description (YadaProduct_id bigint not null, locale varchar(32) not null, description varchar(8192), primary key (YadaProduct_id, locale)) engine=InnoDB;
create table YadaProduct_galleryImages (YadaProduct_id bigint not null, galleryImages_id bigint not null) engine=InnoDB;
create table YadaProduct_materials (YadaProduct_id bigint not null, locale varchar(32) not null, materials varchar(128), primary key (YadaProduct_id, locale)) engine=InnoDB;
create table YadaProduct_name (YadaProduct_id bigint not null, locale varchar(32) not null, name varchar(64), primary key (YadaProduct_id, locale)) engine=InnoDB;
create table YadaProduct_subcategories (YadaProduct_id bigint not null, subcategories_id bigint not null) engine=InnoDB;
create table YadaProduct_subtitle (YadaProduct_id bigint not null, locale varchar(32) not null, subtitle varchar(128), primary key (YadaProduct_id, locale)) engine=InnoDB;
create table YadaRegistrationRequest (registrationType tinyint check (registrationType between 0 and 3), id bigint not null auto_increment, timestamp TIMESTAMP NULL null, token bigint not null, trattamentoDati_id bigint, version bigint not null, yadaUserCredentials_id bigint, email varchar(64) not null, timezone varchar(64), password varchar(128) not null, primary key (id)) engine=InnoDB;
create table YadaSocialCredentials (type integer not null, id bigint not null auto_increment, version bigint not null, yadaUserCredentials_id bigint not null, email varchar(128) not null, socialId varchar(128) not null, primary key (id)) engine=InnoDB;
create table YadaTicket (priority integer not null, assigned_id bigint, creationDate TIMESTAMP NULL null, id bigint not null auto_increment, owner_id bigint not null, status_id bigint, type_id bigint, version bigint not null, title varchar(80), primary key (id)) engine=InnoDB;
create table YadaTicketMessage (id bigint not null, yadaTicket_id bigint not null, primary key (id)) engine=InnoDB;
create table YadaTransaction (external bit, inverse bit, suspended bit, accountOwner_id bigint, amount bigint not null, currencyCode varchar(8), id bigint not null auto_increment, modified DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, order_id bigint, otherParty_id bigint, timestamp TIMESTAMP NULL null, version bigint not null, paymentSystem varchar(32), status varchar(32), data varchar(8192), description varchar(255), payerId1 varchar(255), payerId2 varchar(255), transactionId varchar(255), primary key (id)) engine=InnoDB;
create table YadaUserCredentials (changePassword bit not null, enabled bit not null, failedAttempts integer not null, creationDate TIMESTAMP NULL null, id bigint not null auto_increment, lastFailedAttempt TIMESTAMP NULL null, lastSuccessfulLogin TIMESTAMP NULL null, passwordDate TIMESTAMP NULL null, version bigint not null, password varchar(128) not null, username varchar(128) not null, primary key (id)) engine=InnoDB;
create table YadaUserCredentials_roles (roles integer, YadaUserCredentials_id bigint not null) engine=InnoDB;
create table YadaUserMessage (contentHash integer not null, emailed bit not null, priority integer not null, readByRecipient bit not null, stackSize integer not null, status integer, id bigint not null auto_increment, modified TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL ON UPDATE CURRENT_TIMESTAMP null, recipient_id bigint, sender_id bigint, type_id bigint, version bigint not null, title varchar(80), data varchar(1024), message varchar(12000), primary key (id)) engine=InnoDB;
create table YadaUserMessage_created (YadaUserMessage_id bigint not null, created datetime(6)) engine=InnoDB;
create table YadaUserMessage_YadaAttachedFile (YadaUserMessage_id bigint not null, attachment_id bigint not null) engine=InnoDB;
create table YadaUserProfile (timezoneSetByUser bit not null, avatar_id bigint, id bigint not null auto_increment, userCredentials_id bigint not null, version bigint not null, DTYPE varchar(31) not null, firstName varchar(32), locale varchar(32), middleName varchar(32), nickname varchar(32), lastName varchar(64), timezone varchar(64), primary key (id)) engine=InnoDB;
create table YexRegistrationRequest (id bigint not null, name varchar(255), surname varchar(255), primary key (id)) engine=InnoDB;
alter table YadaArticle add constraint UK_7vechuxf3aovt7qyl8367akf6 unique (image_id);
alter table YadaArticle add constraint UK_nhcjr5g5me98n5drrkfg1p7rc unique (sku);
alter table YadaArticle_attachments add constraint UK_ckp9t4579t7mov1miiu8fql4f unique (attachments_id);
alter table YadaArticle_galleryImages add constraint UK_rl1gkdyfrt43m5jw3u598olgk unique (galleryImages_id);
alter table YadaArticle_silhouetteImages add constraint UK_sq5igv7krtxe04nyyhlx4djeg unique (silhouetteImages_id);
alter table YadaAutoLoginToken add constraint UK_gpwvvntka6p2tjtnut6qiokyi unique (yadaUserCredentials_id);
alter table YadaBrowserId add constraint UKlvfuna79iqujxpkn0l6xvirh4 unique (mostSigBits, leastSigBits);
alter table YadaClause add constraint UKek0brxiv78vf6idvd6dv8v69d unique (name, clauseVersion);
alter table YadaJob add constraint UK_dv98ftndf2nyk42cjoe50i36v unique (jobStateObject_id);
alter table YadaOrder add constraint UK_8iybl8cd5lhj034f5gahfoymk unique (orderStatus_id);
alter table YadaPersistentEnum add constraint UKfuc71vofqasw0r57t7etipp7p unique (enumClassName, enumOrdinal);
alter table YadaProduct add constraint UK_aaflxyksnnr4ut62k1khkw4co unique (image_id);
alter table YadaProduct_attachments add constraint UK_fsu5mkfbdh7dvoq619df26n50 unique (attachments_id);
alter table YadaProduct_categories add constraint UK1cmx2j13ct60yhcy0ef59lny1 unique (YadaProduct_id, categories_id);
alter table YadaProduct_galleryImages add constraint UK_paqmx4kvpgkaxj40cxlbxnv9h unique (galleryImages_id);
alter table YadaProduct_subcategories add constraint UKamh3k42m45dww09m7a27b2qer unique (YadaProduct_id, subcategories_id);
alter table YadaRegistrationRequest add constraint UK_66pbcq6oohrfjqwr1o7wslucn unique (trattamentoDati_id);
alter table YadaRegistrationRequest add constraint UK_b0i98ixarlwa54gkxws9ykxae unique (yadaUserCredentials_id);
alter table YadaSocialCredentials add constraint UK_1uppa4u7bksphbjwm4i2c8re9 unique (socialId);
alter table YadaTicket add constraint UK_bbcda6270hf9977unmh0rt3ua unique (status_id);
alter table YadaTicket add constraint UK_cp5v1tqelobdtcaoeus3lr2r7 unique (type_id);
alter table YadaTransaction add constraint UK_mt2464fv1orqfodyjr5pa8vcv unique (order_id);
alter table YadaUserCredentials add constraint UK_6gbgs7fb7g5t4wo0ys7e5q31j unique (username);
alter table YadaUserMessage add constraint UK_rebeqry21dwm7f3q3yi5xuro1 unique (type_id);
alter table YadaUserMessage_YadaAttachedFile add constraint UK_9jwh2bf420o62f2r5aa5eapo9 unique (attachment_id);
alter table YadaUserProfile add constraint UK_q1b54fx8m1fu9budmpt2tm80i unique (avatar_id);
alter table YadaUserProfile add constraint UK_3bjn82k5gj41f9ocejoxx1uua unique (userCredentials_id);
alter table YadaAddress add constraint FK2eugd1latuu00dmevh5ae6d7l foreign key (owner_id) references YadaUserProfile (id);
alter table YadaArticle add constraint FKh7anw4lfm7mp9aad3aup32vea foreign key (image_id) references YadaAttachedFile (id);
alter table YadaArticle add constraint FK8pj59lffwoe4w2yk8xs0jirmv foreign key (product_id) references YadaProduct (id);
alter table YadaArticle_attachments add constraint FKo5i8wax1h5js7hlmqpl067cbd foreign key (attachments_id) references YadaAttachedFile (id);
alter table YadaArticle_attachments add constraint FKoj11oi1wdda3wac7ovyr9pf60 foreign key (YadaArticle_id) references YadaArticle (id);
alter table YadaArticle_color add constraint FKlvk7va9259rk9ksgxui806jj6 foreign key (YadaArticle_id) references YadaArticle (id);
alter table YadaArticle_galleryImages add constraint FKntidjedo6gwajgeymds0ujd7f foreign key (galleryImages_id) references YadaAttachedFile (id);
alter table YadaArticle_galleryImages add constraint FK946rcfq8u7ueugfdk4lpogb9y foreign key (YadaArticle_id) references YadaArticle (id);
alter table YadaArticle_name add constraint FKj9qpj48f710i50xcdxpddf7nj foreign key (YadaArticle_id) references YadaArticle (id);
alter table YadaArticle_silhouetteImages add constraint FKk6one6qlrw2xqp8wsj0jhail9 foreign key (silhouetteImages_id) references YadaAttachedFile (id);
alter table YadaArticle_silhouetteImages add constraint FKoeebgo3xu503nooo0piyv3nbi foreign key (YadaArticle_id) references YadaArticle (id);
alter table YadaAttachedFile_description add constraint FKj1954nnr3hu07yak1tyb4inc6 foreign key (YadaAttachedFile_id) references YadaAttachedFile (id);
alter table YadaAttachedFile_title add constraint FKqawwx1dakd1a91pxgappdycka foreign key (YadaAttachedFile_id) references YadaAttachedFile (id);
alter table YadaAutoLoginToken add constraint FKh92vo7me2k2s4v1x1jercpuo foreign key (yadaUserCredentials_id) references YadaUserCredentials (id);
alter table YadaCart add constraint FK4cloq1osjtfptmn6o5o6mxbr0 foreign key (owner_id) references YadaUserProfile (id);
alter table YadaCartItem add constraint FK8cmseq85t4w2bu1435v8olke1 foreign key (article_id) references YadaCommerceArticle (id);
alter table YadaCartItem add constraint FK632j50kx6vglg5kp3kpn155o4 foreign key (cart_id) references YadaCart (id);
alter table YadaCommentMessage add constraint FK8pjtjjsqyiig5cstsnl4j9wgc foreign key (repliesTo_id) references YadaCommentMessage (id);
alter table YadaCommentMessage add constraint FKbgop4huf0whlecr7fahr7a5jl foreign key (id) references YadaUserMessage (id);
alter table YadaCommentMessageLiked add constraint FKfed09r49te9ap2l3l5elqvd0d foreign key (YadaUserProfile_id) references YadaUserProfile (id);
alter table YadaCommentMessageLiked add constraint FKdmh4adj471o0d1exy0w16m4f1 foreign key (YadaCommentMessage_id) references YadaCommentMessage (id);
alter table YadaCommerceArticle add constraint FKswe9u6evi2ltnb1qwenv77smd foreign key (id) references YadaArticle (id);
alter table YadaJob add constraint FKbly4fv9jmbvwppy5b9x79yokq foreign key (jobStateObject_id) references YadaPersistentEnum (id);
alter table YadaJob_BeActive add constraint FKfcdajxue4qegy3sh412qcqd7 foreign key (jobsMustBeActive_id) references YadaJob (id);
alter table YadaJob_BeActive add constraint FKqhqlee0k5m0ir9s6kpw8m9y6d foreign key (YadaJob_id) references YadaJob (id);
alter table YadaJob_BeCompleted add constraint FK8o25xd851myc035dwd0xm7kpd foreign key (jobsMustComplete_id) references YadaJob (id);
alter table YadaJob_BeCompleted add constraint FKgcmntp7yy872ldenedb6nnyep foreign key (YadaJob_id) references YadaJob (id);
alter table YadaJob_BeInactive add constraint FK8yfnn9cj06lrptwbtnpnevp4h foreign key (jobsMustBeInactive_id) references YadaJob (id);
alter table YadaJob_BeInactive add constraint FKamylqhhgf9gjwsux3yfosvq52 foreign key (YadaJob_id) references YadaJob (id);
alter table YadaOrder add constraint FKf1vte0esagjxmtc62jj0k98ry foreign key (orderStatus_id) references YadaPersistentEnum (id);
alter table YadaOrder add constraint FK3u96txslj6i2343hhsh0rxb4m foreign key (owner_id) references YadaUserProfile (id);
alter table YadaOrderItem add constraint FKnjpsd15nt8csnpojhhd4jtpif foreign key (order_id) references YadaOrder (id);
alter table YadaPersistentEnum_langToText add constraint FKewmgshpqaehgfba9sp8pluddg foreign key (YadaPersistentEnum_id) references YadaPersistentEnum (id);
alter table YadaProduct add constraint FKen9btb905njlidk3auvewqrw0 foreign key (image_id) references YadaAttachedFile (id);
alter table YadaProduct_accessories add constraint FKc0fib48o5ohspki155jt5ijad foreign key (accessories_id) references YadaProduct (id);
alter table YadaProduct_accessories add constraint FKlrtfnagonn4o4j5y1no2qdn0p foreign key (accessoryOf_id) references YadaProduct (id);
alter table YadaProduct_attachments add constraint FK4tpdxk79xwmx79p9moiehi011 foreign key (attachments_id) references YadaAttachedFile (id);
alter table YadaProduct_attachments add constraint FKmrnigiegv3tpcjg1i0y7or2gs foreign key (YadaProduct_id) references YadaProduct (id);
alter table YadaProduct_categories add constraint FKpo9g7k6ryamt1npavvwpj1wdb foreign key (categories_id) references YadaPersistentEnum (id);
alter table YadaProduct_categories add constraint FK49lk07l13fw2ka8ag9nurfawa foreign key (YadaProduct_id) references YadaProduct (id);
alter table YadaProduct_description add constraint FK9rto4a8un9vt79me4qoq2p4sv foreign key (YadaProduct_id) references YadaProduct (id);
alter table YadaProduct_galleryImages add constraint FK8scjcwfj0kqbbosdei8hlcqon foreign key (galleryImages_id) references YadaAttachedFile (id);
alter table YadaProduct_galleryImages add constraint FKhalpejij8cy1n8w23lpac5yhu foreign key (YadaProduct_id) references YadaProduct (id);
alter table YadaProduct_materials add constraint FKet1a1eniesxql0b0p7xr1y876 foreign key (YadaProduct_id) references YadaProduct (id);
alter table YadaProduct_name add constraint FK56ctdnwejlx41fs5olg56j4hg foreign key (YadaProduct_id) references YadaProduct (id);
alter table YadaProduct_subcategories add constraint FKau9b0n8mnvm1dmobeo7omcqkl foreign key (subcategories_id) references YadaPersistentEnum (id);
alter table YadaProduct_subcategories add constraint FK1nnwcuaxq0bea0hgt5nqutn3x foreign key (YadaProduct_id) references YadaProduct (id);
alter table YadaProduct_subtitle add constraint FK2u6n4iq79g3vhl0h2v1322nns foreign key (YadaProduct_id) references YadaProduct (id);
alter table YadaRegistrationRequest add constraint FKkn2yxfy3t9fjmuannqfph49d0 foreign key (trattamentoDati_id) references YadaClause (id);
alter table YadaRegistrationRequest add constraint FKq6guqxscpqqq7pl96md1y79rn foreign key (yadaUserCredentials_id) references YadaUserCredentials (id);
alter table YadaSocialCredentials add constraint FK72s54ufexgh2xk2122ihkc82l foreign key (yadaUserCredentials_id) references YadaUserCredentials (id);
alter table YadaTicket add constraint FKgxcmctrd5k7jovlu497fnrrh8 foreign key (assigned_id) references YadaUserProfile (id);
alter table YadaTicket add constraint FKpt79m8p0ph2e9x3teolffifi7 foreign key (owner_id) references YadaUserProfile (id);
alter table YadaTicket add constraint FKg7yv00ti2fq2dgjgvt0pn8f7n foreign key (status_id) references YadaPersistentEnum (id);
alter table YadaTicket add constraint FKk8byguth335pdh92tndbvk1u9 foreign key (type_id) references YadaPersistentEnum (id);
alter table YadaTicketMessage add constraint FKdhovs8wxvntcio77lqjby2rx0 foreign key (yadaTicket_id) references YadaTicket (id);
alter table YadaTicketMessage add constraint FK8w0ggxjhuvd506sfos27w1ys4 foreign key (id) references YadaUserMessage (id);
alter table YadaTransaction add constraint FK7kmc6mwua2vteo5s9vptexca6 foreign key (accountOwner_id) references YadaUserProfile (id);
alter table YadaTransaction add constraint FKfj43ln0goq17ltkoikg53u3f1 foreign key (order_id) references YadaOrder (id);
alter table YadaTransaction add constraint FK5fe7cs2i2fjgbn5e08gp4rht3 foreign key (otherParty_id) references YadaUserProfile (id);
alter table YadaUserCredentials_roles add constraint FK1oj60uojdn4xql004wfe2v0hp foreign key (YadaUserCredentials_id) references YadaUserCredentials (id);
alter table YadaUserMessage add constraint FKjq0kwximnydvb58rn783hchjk foreign key (recipient_id) references YadaUserProfile (id);
alter table YadaUserMessage add constraint FKn73jmjlct9vb6rwehg00kl42i foreign key (sender_id) references YadaUserProfile (id);
alter table YadaUserMessage add constraint FKlvju0l32ov4mhe89qmhowv2sr foreign key (type_id) references YadaPersistentEnum (id);
alter table YadaUserMessage_created add constraint FK1tknniq7escftsaqpkh7mt8q8 foreign key (YadaUserMessage_id) references YadaUserMessage (id);
alter table YadaUserMessage_YadaAttachedFile add constraint FKrmqyj2dm4pd687whypdn3xygg foreign key (attachment_id) references YadaAttachedFile (id);
alter table YadaUserMessage_YadaAttachedFile add constraint FKohvurjoh3cboeujrn517v97ax foreign key (YadaUserMessage_id) references YadaUserMessage (id);
alter table YadaUserProfile add constraint FKpi28ogwa7vguwb3vv1tkmpovi foreign key (avatar_id) references YadaAttachedFile (id);
alter table YadaUserProfile add constraint FKm8x7qmacvae25wmfdhnuf4e25 foreign key (userCredentials_id) references YadaUserCredentials (id);
alter table YexRegistrationRequest add constraint FK1ji9ebmpp50q9h53ijdnmd51y foreign key (id) references YadaRegistrationRequest (id);
