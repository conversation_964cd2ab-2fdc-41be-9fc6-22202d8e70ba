
<config>
	<info>
		<env>dev</env>
		<appName>YadaExamples</appName>
		<version>0.1 alpha</version>
		<build>${build}</build>
		<date>@@RELEASEDATE_PLACEHOLDER@@</date> <!-- Injected by gradle -->
	</info>
	<paths>
		<serverAddress>http://dev.EXAMPLE.com/</serverAddress>
		<basePath>/srv/yexdev</basePath>
		<contentDir name="contents" url="/contents"></contentDir>
	</paths>
	<!-- Define embedded tomcat parameters. Use when more than one application is running on the same server. 
	<tomcat>
		<ports>
			<http></http>
			<https></https>
			<ajp></ajp>
			<ajpRedirect></ajpRedirect>
			<shutdown></shutdown>
		</ports>
		<keystore>
			<file></file>
			<password></password>
		</keystore>
	</tomcat>
	-->
	<email>
		<enabled>true</enabled>
		<from>
			<address><EMAIL></address>
			<name>EXAMPLE SENDER</name>
		</from>
		
		<support>
			<!-- Addresses to which a support request is sent -->
			<to><EMAIL></to>
		</support>
		<logoImage>template/email/logo50.png</logoImage>
		<smtpserver>
			<host>smtp.EXAMPLE.com</host>
			<port>587</port>
			<protocol>smtp</protocol>
			<username>${smtpserver_username}</username>
			<password>${smtpserver_password}</password>
			<!--  If set to true, and a message has some valid and some invalid addresses, send the message anyway, reporting the partial failure with a SendFailedException. If set to false (the default), the message is not sent to any of the recipients if there is an invalid recipient address.  -->
			<properties>mail.smtp.sendpartial=true</properties> 
			<properties>mail.smtp.auth=true</properties>
			<properties>mail.smtp.starttls.enable=true</properties>
			<properties>mail.smtp.quitwait=false</properties>
			<!-- Set this option to your mail server if you trust it and don't care checking the certificate validity, e.g. it is on your localhost -->
			<properties>mail.smtp.ssl.trust=smtp.EXAMPLE.com</properties>
		</smtpserver>
		<!-- Remove this list to enable email to everyone -->
		<validEmail><EMAIL></validEmail>
		<validEmail><EMAIL></validEmail>
		<validEmail><EMAIL></validEmail>
	</email>

	<database enabled="true">
		<embedded enabled="false">
			<datadir>/srv/yexdev/embeddedDB</datadir>
			<!-- Path of the sql file that will be loaded at startup -->
			<sourceSql>/srv/yexdev/source.sql</sourceSql>
		</embedded>
		<jndiname>java:comp/env/jdbc/yexdbdev</jndiname> <!-- Not used when <datasource> is defined -->
		<showSql>false</showSql>
		
		<!-- Datasource for embedded Tomcat -->
		<datasource>
			<jdbcUrl>****************************************************************************************************************************************************</jdbcUrl>
			<username>yexuserdev</username>
			<password>qwe</password>
			
			<name>yexdevpool</name>
			<poolInitialSize>10</poolInitialSize>	
			<poolMaxSize>100</poolMaxSize>
			<poolEnableConnectionTracking>true</poolEnableConnectionTracking>
			
			<logQueryExecutionLongerThanMs>2000</logQueryExecutionLongerThanMs>
			<logStackTraceForLongQueryExecution>true</logStackTraceForLongQueryExecution>
			<logLargeResultSet>500</logLargeResultSet> <!-- ResultSets with length greater than or equal to this limit are logged -->
			<logStackTraceForLargeResultSet>true</logStackTraceForLargeResultSet>
			<includeQueryParameters>true</includeQueryParameters>
			
			<statementCacheMaxSize>200</statementCacheMaxSize>
		</datasource>
	</database>

	<security>
		<!-- sessionTimeoutMinutes is used via javascript to check if the browser session is expired and show a warning modal -->
		<!-- WARNING: this value must be the same as the value of session-timeout in /src/main/webapp/WEB-INF/web.xml or the session might never expire -->
		<sessionTimeoutMinutes>240</sessionTimeoutMinutes>
		<passwordLength min='5' max='128' />
		<encodePassword>true</encodePassword>
		<maxFailedAttempts>10</maxFailedAttempts>
		<failedAttemptsLockoutMinutes>10</failedAttemptsLockoutMinutes>
		<autologinExpirationHours>48</autologinExpirationHours>
	</security>
</config>