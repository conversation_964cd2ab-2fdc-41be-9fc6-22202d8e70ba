<!-- Logback configuration for development environments -->
<configuration scan="true" scanPeriod="5 seconds" debug="false">
	<logger name="ch.qos.logback" level="warn"/>

	<appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>/srv/yexdev/logs/site.log</file>
	 	<rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
	    	<fileNamePattern>/srv/yexdev/logs/site.%i.log</fileNamePattern>
			<minIndex>1</minIndex>
			<maxIndex>9</maxIndex>
	    </rollingPolicy>
    	<triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
      		<maxFileSize>20MB</maxFileSize>
    	</triggeringPolicy>
		<encoder>
			<pattern>%d :%X{session}:%X{remoteIp}:%X{username} [%thread] %-5level %logger{36} - %msg%n
			</pattern>
		</encoder>
	</appender>

	<appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
		<encoder>
			<pattern>dev %d{HH:mm:ss} %-5level %logger{36} - %X{mdc}: %msg%n</pattern>
		</encoder>
	</appender>

	<appender name="SEC" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>/srv/yexdev/logs/sec.log</file>
	 	<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
	    	<fileNamePattern>/srv/yexdev/logs/sec.%d.log</fileNamePattern>
			<maxHistory>7</maxHistory>
	    </rollingPolicy>
		<encoder>
			<pattern>%d :%X{session}:%X{remoteIp}:%X{username} [%thread] %-5level %logger{36} - %msg%n
			</pattern>
		</encoder>
	</appender>

	<!-- 
  <appender name="SEC" class="ch.qos.logback.core.ConsoleAppender">
    <encoder>
      <pattern>security %d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
    </encoder>
  </appender>
	 -->

    <logger name="org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping" level="ERROR"/>

	<logger name="org.hibernate.SQL" level="DEBUG" /> <!-- Same as hibernate.show_sql=true -->
    
    <logger name="security" level="debug" additivity="true">
    	<appender-ref ref="SEC" />
    </logger>

    <logger name="org.apache.commons.configuration.DefaultConfigurationBuilder" level="ERROR"/>
    
    <!-- To hide WARN: bad write method arg count: public final void org.apache.commons.configuration2.AbstractConfiguration.setProperty -->
    <logger name="org.apache.commons.beanutils.FluentPropertyBeanIntrospector" level="ERROR"/> 
    
    <logger name="net.yadaframework.example" level="debug"/>
    
	<root level="info">
		<appender-ref ref="STDOUT" />
		<appender-ref ref="FILE" />
	</root>

	<!-- Developer personal settings -->
	<!-- 
	<include resource="logback-personal.xml" />
	 -->
</configuration>