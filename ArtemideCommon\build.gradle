// Apply the java-library plugin to add support for Java Library
apply plugin: 'java-library'
apply plugin: 'eclipse-wtp'

compileJava.options.encoding = 'UTF-8'
compileTestJava.options.encoding = 'UTF-8'

java {
    sourceCompatibility = JavaVersion.VERSION_21
    targetCompatibility = JavaVersion.VERSION_21
	toolchain {
		languageVersion = JavaLanguageVersion.of(21)
	}
	// https://docs.gradle.org/6.0/userguide/java_plugin.html#sec:java-extension
	withSourcesJar()
    withJavadocJar() // https://stackoverflow.com/a/75710366/587641
}

repositories {
    // jcenter()
    mavenCentral()
}

dependencies {
    // This dependency is exported to consumers, that is to say found on their compile classpath.
    // api 'org.apache.commons:commons-math3:3.6.1'
	
    // This dependency is used internally, and not exposed to consumers on their own compile classpath.
    // implementation 'com.google.guava:guava:21.0'
	implementation project(':YadaWeb'), project(':YadaWebSecurity'), project(':YadaWebCMS'),
		'org.springframework:spring-webmvc:6.2.10',
		'org.springframework.data:spring-data-jpa:3.5.3',
		'org.hibernate.orm:hibernate-core:7.1.0.Final',
		'org.hibernate.validator:hibernate-validator:8.0.1.Final',
		'net.sourceforge.jexcelapi:jxl:2.6.12',
		// Log4j is referenced by jxl, so we ensure the latest version
		'org.apache.logging.log4j:log4j:2.25.1',
		'com.mortennobel:java-image-scaling:0.8.6',
		// 'commons-beanutils:commons-beanutils-core:1.8.3',
		'org.springframework.security:spring-security-core:6.5.3',
		'org.apache.tomcat:tomcat-servlet-api:11.0.10',
		'commons-io:commons-io:2.16.1',
		'org.apache.commons:commons-collections4:4.5.0-M2',
		'org.apache.commons:commons-exec:1.4.0',
		'com.google.guava:guava:33.4.0-jre',
		'com.fasterxml.jackson.core:jackson-databind:2.19.2',
		'ch.qos.logback:logback-classic:1.5.18',
		'org.apache.httpcomponents.client5:httpclient5:5.4.1',
		'jakarta.annotation:jakarta.annotation-api:3.0.0'
		

    // Use JUnit test framework
    // testImplementation 'junit:junit:4.12'
}


