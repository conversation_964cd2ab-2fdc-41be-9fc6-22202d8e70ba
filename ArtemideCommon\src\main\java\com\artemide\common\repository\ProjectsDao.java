package com.artemide.common.repository;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.data.domain.SliceImpl;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.artemide.common.components.AmdUtil;
import com.artemide.common.persistence.entity.Project;
import com.artemide.common.persistence.entity.ProjectModule;
import com.artemide.common.persistence.entity.ProjectTag;

import net.yadaframework.components.YadaFileManager;
import net.yadaframework.components.YadaUtil;
import net.yadaframework.persistence.YadaSql;
import net.yadaframework.persistence.entity.YadaAttachedFile;

@Repository
@Transactional(readOnly = true)
public class ProjectsDao {

    @PersistenceContext EntityManager em;

    @Autowired private YadaFileManager yadaFileManager;

	/**
	 * Scarica le immagini del progetto e quelle dei suoi moduli.
	 * @param project
	 * @param model
	 * @param locale
	 * @return
	 */
	public List<YadaAttachedFile> getFileList(Project project) {
		List<YadaAttachedFile> filesToZip = new ArrayList<>();

		if (project.getId()!=null) {

			project = em.merge(project);

			filesToZip.add(project.getBigImage());
			filesToZip.add(project.getSmallImage());

			List<ProjectModule> projectModules = project.getProjectModules();
			for (ProjectModule projectModule : projectModules) {
				if (projectModule.getType() == 9) {
					List<YadaAttachedFile> carrouselImages = projectModule.getCarrouselImages();
					for (YadaAttachedFile yadaAttachedFile : carrouselImages) {
						filesToZip.add(yadaAttachedFile);
					}
				} else {
					if (projectModule.getImage()!=null) {
						filesToZip.add(projectModule.getImage());
					}
					if (projectModule.getImageTwo()!=null) {
						filesToZip.add(projectModule.getImageTwo());
					}
				}
			}

		}

		return filesToZip;
	}

    /**
     * Cancella una Project
     * @param Project
     */
    @Transactional(readOnly = false)
    public String delete(Long id, Locale locale) {
    	Project p = em.find(Project.class, id);
    	deleteTagByProjectId(id);

    	String name = p.getLocalTitlePartOne();
    	em.remove(p); //dovrebbe cancellare anche i moduli.
        return name;
    }

    /**
     * cancella la relazione tra Tag e NewsId senza cancellare il vero Tag
     * @param projectId
     */
    @Transactional(readOnly = false)
    public void deleteTagByProjectId(Long projectId) {
    	performQuery(em.createNativeQuery("delete from Tag_Project where project_id = :projectId"), projectId);
    }

    /**
     * Cancella una ProjectModule
     * @param Project
     */
    @Transactional(readOnly = false)
    public Project deleteProjectModule(Long projectModuleId, Project project) {
    	project = em.merge(project);
    	List<ProjectModule> projectModules = project.getProjectModules();
    	for (ProjectModule pm : projectModules) {
    		if (pm.getId().equals(projectModuleId)) {
    			projectModules.remove(pm);
    			em.remove(pm);
    			break;
    		}
    	}
    	return project;
    }

    private void performQuery(Query query, Long id) {
    	query.setParameter("projectId", id);
    	query.executeUpdate();
    }

    /**
     * Duplicate a News
     * @param project
     * @return
     * @throws IOException
     */
    @Transactional(readOnly = false)
    public Project duplicate(Project project) throws IOException {
    	project = em.merge(project); // serve?
    	Project copy = (Project) YadaUtil.copyEntity(project);
    	String copyLocalTitle = project.getLocalTitlePartOne() + " (copy)";
    	copy.setLocalTitlePartOne(copyLocalTitle);
    	// I tag sono stati copiati nella nuova project ma poiché la relazione nel db è mantenuta dal tag e non dalla project, al salvataggio la relazione si perde.
    	// E' necessario quindi iterare sui tag e aggiungere la project
    	for (ProjectTag tag : copy.getTags()) {
			tag.getProject().add(copy);
		}
    	em.persist(copy);
    	return copy;
    }

	@Transactional(readOnly = false)
	public Project addProjectModule(Project project, ProjectModule projectModule, int type) {
		project = em.merge(project);
		List<ProjectModule> projectModules = project.getProjectModules();
		projectModule.setType(type);
		projectModule.setProject(project);
		em.persist(projectModule);
		projectModules.add(projectModule);
		return project;
	}

	@Transactional(readOnly = false)
	public Project duplicateProjectModule(Project project, ProjectModule projectModule) {
		project = em.merge(project);
		projectModule = em.merge(projectModule);
		List<ProjectModule> projectModules = project.getProjectModules();
		ProjectModule copy = (ProjectModule) YadaUtil.copyEntity(projectModule);
		em.persist(copy);
		projectModules.add(copy);
		return project;
	}

	/**
	 * Restituice una Project per l'header, dandoli come parametro true se vogliamo trovare subito quella project in cui è settato come topNews
	 * altrimenti se è null allora torna la prima disponibile in ordine di published date
	 * @param topNews
	 * @return
	 */
	@Deprecated // Credo non lo usi nessuno [xtian]
	public Project findOneEnabledTrueToHeaderPage(boolean topNews) { //topNews = true if
		YadaSql yadaSql = YadaSql.instance().selectFrom("from Project p")
				.join("join fetch p.titlePartOne")
				.join("join fetch p.titlePartTwo")
				.where(!AmdUtil.isPreview(),"where p.publishDate <= NOW()").and()
				.where(!AmdUtil.isPreview(),"where p.enabled = true").and()
		    	.orderBy("p.publishDate desc, p.id desc")
		    	.limit(1); //solo 1 x l'header

		try {
			return yadaSql.query(em, Project.class).getSingleResult();
		} catch (NoResultException e) {
			if (topNews==true) {
				return findOneEnabledTrueToHeaderPage(false);
			}
			return null;
		}
	}

	/**
	 * Trova tutti i projects.
	 * @param tagName
	 * @param pageable
	 * @return
	 */
	public Slice<Project> findProjects(String tagName, Pageable pageable) {
		return findProjects(tagName, pageable, false, false, false, false);
	}
	
	public Slice<Project> findProjectsAndLoadImageDescription(String tagName, Pageable pageable) {
		return findProjects(tagName, pageable, false, false, false, false, true);
	}
	
	/**
	 * Serve per popolare la sitemap. Inserendo a true lo scenario, allora prendo tutti i scenario, senza il bisogno di filtrare per categorie.
	 * @param tagName
	 * @param pageable
	 * @return
	 */
	public Slice<Project> findScenarios(String tagName, Pageable pageable) {
		return findProjects(tagName, pageable, true, false, false, false);
	}
	
	
	public Slice<Project> findProjects(String tagName, Pageable pageable, boolean scenarios, boolean bespoken, boolean sartorial, boolean selected) {
		return this.findProjects(tagName, pageable, scenarios, bespoken, sartorial, selected, false);
	}	
	/**
	 *
	 * @param tagName
	 * @param pageable
	 * @return
	 */
	public Slice<Project> findProjects(String tagName, Pageable pageable, boolean scenarios, boolean bespoken, boolean sartorial, boolean selected, boolean loadImageDescription) {
		YadaSql yadaSql = YadaSql.instance().selectFrom("select distinct p from Project p")
//				.join("join fetch p.titlePartOne")
//				.join("join fetch p.titlePartTwo")
				//.join("join fetch n.projectModules")
				.join(tagName!=null && !tagName.isEmpty(), "join p.tags t")
				.where(!AmdUtil.isPreview(),"where p.publishDate <= NOW()").and()
				.where(!AmdUtil.isPreview(),"where p.enabled = true").and()
				.where(scenarios, "p.scenarios=true").and()
				.where(!scenarios, "p.scenarios=false").and()
				.where(bespoken, "p.bespoken=true").and()
				.where(sartorial, "p.sartorial=true").and()
				.where(selected, "p.selected=true").and()
		    	.where(tagName!=null && !tagName.isEmpty(), ":tagName member of t.name").and()
		    	// .where(tagName==null || tagName.isEmpty(), "'' not member of p.tags.name")
		    	// ATTENZIONE: qui è necessario mettere anche "id desc" perché su linux quando le publishDate sono identiche il MySql si confonde
		    	.orderBy("p.publishDate desc, p.id desc")
		    	.setParameter("tagName", tagName);

			@SuppressWarnings("unchecked")
			int pageSize = pageable.getPageSize();
			int offset= (int) pageable.getOffset();
			if (offset==0) {
				pageSize++; // La prima pagina deve avere sei elementi e non cinque come tutte le altre
			} else {
				offset++; // Nelle pagine successive si salta uno perché la prima pagina è di 6 e non di 5
			}
	    	List<Project> found = yadaSql.query(em, Project.class)
				.setFirstResult(offset)
				.setMaxResults(pageSize)
				.getResultList();

			if (loadImageDescription) {
				YadaUtil.prefetchLocalizedStringListRecursive(found, Project.class);
			}

	    	Pageable nextPage = pageable.next();
	    	boolean hasNext = found.size()==pageSize; //pageable.getPageSize(); // false se non ci sono altri elementi
	    	if (hasNext==true) {
	    		// Controlliamo se veramente ci sono altri elementi dopo
	    		List<Project> nextElement = yadaSql.query(em, Project.class)
		    			.setFirstResult((int) nextPage.getOffset() + 1)
		    			.setMaxResults(1)
		    			.getResultList();
	    		hasNext = !nextElement.isEmpty();
	    	}


		Slice<Project> result = new SliceImpl<>(found, nextPage, hasNext);
    	return result;
	}

	/**
     * Trova la Project precedente o successivo. Il tagName serve per sapere il suo tag e per sapere se invace a questo tag se ci sono altri
     * progetti con il medessimo tag prima o dopo al progetto
     * @param previous true per il previous, false per il next
     * @param project
     * @param pageable
     * @return
     */
	public Slice<Project> findWithLocalitasPreviousNext(String tagName, boolean previous, Project project, Pageable pageable) {
		return findScenariosWithLocalitasPreviousNext(tagName, previous, project, pageable, false, false, false);
	}

	/**
	 * Trova la Project precedente o successivo
	 * @param previous true per il previous, false per il next
	 * @param project
	 * @param pageable
	 * @return
	 */
	public Slice<Project> findScenariosWithLocalitasPreviousNext(String tagName, boolean previous, Project project, Pageable pageable, boolean bespoken, boolean sartorial, boolean selected) {
		boolean scenarios = bespoken || sartorial || selected;
		AmdYadaSql yadaSql = (AmdYadaSql) AmdYadaSql.instance().selectFrom("from Project p join fetch p.titlePartOne")
				.join("join p.tags t")
				.where("where p<>:project").and()
				.where(!AmdUtil.isPreview(),"where p.enabled = true").and()
				.where(scenarios, "p.scenarios=true").and()
				.where(!scenarios, "p.scenarios=false").and()
				.where(bespoken, "p.bespoken=true").and()
				.where(sartorial, "p.sartorial=true").and()
				.where(selected, "p.selected=true").and()
				.where(previous && !AmdUtil.isPreview(), "p.publishDate >= :publishDate").and()
				.where(!previous && !AmdUtil.isPreview(), "p.publishDate <= :publishDate").and()
				.where(tagName!=null && !tagName.isEmpty(), ":tagName member of t.name")
		    	.where(tagName==null || tagName.isEmpty(), "'' not member of t.name")
				.limit(pageable.getPageSize())
				.setParameter("tagName", tagName)
				.setParameter("project", project)
				.setParameter("publishDate", project.getPublishDate());
		yadaSql.orderBy(pageable);
		List<Project> result = yadaSql.query(em, Project.class).setFirstResult((int) pageable.getOffset()).getResultList();
		Slice<Project> resultPage = new PageImpl<>(result, pageable, 0);
		return resultPage;
	}


	/**
	 * Torna i nomi dei tags in cui ci siano dei scenarios
	 * @param tagName
	 * @param pageable
	 * @return
	 */
	public List<String> findScenariosWihtTagsLocalName(Locale locale, boolean bespoken, boolean sartorial, boolean selected) {
		YadaSql yadaSql = YadaSql.instance().selectFrom("select g.name from Tag_name g ")
				.join("right join Tag t on g.Tag_id = t.id")
				.where("WHERE t.id in (select distinct tp.tags_id from Tag_Project tp join Project p on p.id = tp.project_id "
						+ "WHERE p.scenarios = 1 ").and()
				.where(bespoken==true, "p.bespoken=true").and()
				.where(sartorial==true, "p.sartorial=true").and()
				.where(selected==true, "p.selected=true").and()
				.where("p.enabled=true and p.publishDate <= NOW()) and g.locale = :locale").and()
		    	.orderBy("t.pos asc")
		    	.setParameter("locale", locale);

			@SuppressWarnings("unchecked")
	    	List<String> result = yadaSql.nativeQuery(em).getResultList();
   	return result;
	}

	public Project explicitlyMarkImageAltChanged(Project project) {
	    if (project.getBigImage() != null) {
	        YadaAttachedFile managedBigImage = em.merge(project.getBigImage());
	        project.setBigImage(managedBigImage);
	    }
	    if (project.getSmallImage() != null) {
	        YadaAttachedFile managedSmallImage = em.merge(project.getSmallImage());
	        project.setSmallImage(managedSmallImage);
	    }
	    return em.merge(project); 
	}
}
