package com.artemide.common.repository;

import java.io.IOException;
import java.util.List;
import java.util.Locale;

import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.data.domain.SliceImpl;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.artemide.common.components.AmdUtil;
import com.artemide.common.persistence.entity.NewsJournal;
import com.artemide.common.persistence.entity.NewsModule;
import com.artemide.common.persistence.entity.NewsTag;

import net.yadaframework.components.YadaFileManager;
import net.yadaframework.components.YadaUtil;
import net.yadaframework.persistence.YadaSql;

@Repository
@Transactional(readOnly = true)
public class NewsDao {

    @PersistenceContext EntityManager em;

    @Autowired private YadaFileManager yadaFileManager;

    /**
     * Cancella una News
     * @param NewsJournal
     */
    @Transactional(readOnly = false)
    public String delete(Long id, Locale locale) {
    	NewsJournal n = em.find(NewsJournal.class, id);
    	deleteTagByNewsId(id);

    	String name = n.getLocalTitlePartOne();
    	em.remove(n); //dovrebbe cancellare anche i moduli.
        return name;
    }

    /**
     * cancella la relazione tra Tag e NewsId senza cancellare il vero Tag
     * @param newsId
     */
    @Transactional(readOnly = false)
    public void deleteTagByNewsId(Long newsId) {
    	performQuery(em.createNativeQuery("delete from Tag_NewsJournal where news_id = :newsId"), newsId);
    }

    /**
     * Cancella una NewsModule
     * @param News
     */
    @Transactional(readOnly = false)
    public NewsJournal deleteNewsModule(Long newsModuleId, NewsJournal news) {
    	news = em.merge(news);
    	List<NewsModule> newsModules = news.getNewsModules();
    	for (NewsModule nm : newsModules) {
    		if (nm.getId().equals(newsModuleId)) {
    			newsModules.remove(nm);
    			em.remove(nm);
    			break;
    		}
    	}
    	return news;
    }

    private void performQuery(Query query, Long id) {
    	query.setParameter("newsId", id);
    	query.executeUpdate();
    }

    /**
     * Duplicate a News
     * @param news
     * @return
     * @throws IOException
     */
    @Transactional(readOnly = false)
    public NewsJournal duplicate(NewsJournal news) throws IOException {
    	news = em.merge(news); // serve?
    	NewsJournal copy = (NewsJournal) YadaUtil.copyEntity(news);
    	copy.setTopNews(false);
    	String copyLocalTitle = news.getLocalTitlePartOne() + " (copy)";
    	copy.setLocalTitlePartOne(copyLocalTitle);
    	// I tag sono stati copiati nella nuova news ma poiché la relazione nel db è mantenuta dal tag e non dalla news, al salvataggio la relazione si perde.
    	// E' necessario quindi iterare sui tag e aggiungere la news
    	for (NewsTag tag : copy.getTags()) {
			tag.getNews().add(copy);
		}
    	em.persist(copy);
    	return copy;
    }

	@Transactional(readOnly = false)
	public NewsJournal addNewsModule(NewsJournal news, NewsModule newsModule, int type) {
		news = em.merge(news);
		List<NewsModule> newsModules = news.getNewsModules();
		newsModule.setType(type);
		newsModule.setNews(news);
		em.persist(newsModule);
		newsModules.add(newsModule);
		return news;
	}

	@Transactional(readOnly = false)
	public NewsJournal duplicateNewsModule(NewsJournal news, NewsModule newsModule) {
		news = em.merge(news);
		newsModule = em.merge(newsModule);
		List<NewsModule> newsModules = news.getNewsModules();
		NewsModule copy = (NewsModule) YadaUtil.copyEntity(newsModule);
		em.persist(copy);
		newsModules.add(copy);
		return news;
	}

	/**
	 * Restituice una NewsJournal per l'header, dandoli come parametro true se vogliamo trovare subito quella news in cui è settato come topNews
	 * altrimenti se è null allora torna la prima disponibile in ordine di published date
	 * @param topNews
	 * @return
	 */
	public NewsJournal findOneEnabledTrueToHeaderPage(boolean topNews) { //topNews = true if
		return this.findOneEnabledTrueToHeaderPage(topNews, false);
	}
	
	public NewsJournal findOneEnabledTrueToHeaderPage(boolean topNews, boolean loadImageDescriptions) { //topNews = true if
		YadaSql yadaSql = YadaSql.instance().selectFrom("from NewsJournal n")
				// Ottimizzo per risolvere "HHH000104: firstResult/maxResults..."
				// .join("join fetch n.titlePartOne")
				// .join("join fetch n.titlePartTwo")
				.where(!AmdUtil.isPreview(),"where n.publishDate <= NOW()").and()
				.where(!AmdUtil.isPreview(),"where n.enabled = true").and()
				.where(topNews,"where n.topNews = true").and()
		    	.orderBy("n.showDate desc, n.publishDate desc")
		    	.limit(1); //solo 1 x l'header

		try {
			NewsJournal singleResult = yadaSql.query(em, NewsJournal.class).getSingleResult();
			singleResult.getTitlePartOne().size(); // Prefetch
			singleResult.getTitlePartTwo().size(); // Prefetch
			
			if (loadImageDescriptions) {
				YadaUtil.prefetchLocalizedStringsRecursive(singleResult, NewsJournal.class);
			}
			
			return singleResult;
		} catch (NoResultException e) {
			if (topNews==true) {
				return findOneEnabledTrueToHeaderPage(false);
			}
			return null;
		}
	}

	/**
	 *
	 * @param tagName
	 * @param pageable
	 * @return
	 */
	public Slice<NewsJournal> findNewsJournals(String tagName, Pageable pageable) {
		return this.findNewsJournals(tagName, pageable);
	}
	
	public Slice<NewsJournal> findNewsJournals(String tagName, Pageable pageable, boolean loadImageDescriptions) {
		YadaSql yadaSql = YadaSql.instance().selectFrom("select n from NewsJournal n")
				.join("join fetch n.titlePartOne")
				.join("join fetch n.titlePartTwo")
				.join(tagName!=null && !tagName.isEmpty(), "join n.tags t")
				.where(!AmdUtil.isPreview(),"where n.publishDate <= NOW()").and()
				.where(!AmdUtil.isPreview(),"where n.enabled = true").and()
				.where(!AmdUtil.isPreview(),"where n.unlisted = false").and()
		    	.where(tagName!=null && !tagName.isEmpty(), ":tagName member of t.name").and()
		    	// .where(tagName==null || tagName.isEmpty(), "'' not member of n.tags.name")
		    	.orderBy("n.showDate desc, n.publishDate desc")
		    	.setParameter("tagName", tagName);

			@SuppressWarnings("unchecked")
	    	List<NewsJournal> found = yadaSql.query(em)
				.setFirstResult((int) pageable.getOffset())
				.setMaxResults(pageable.getPageSize())
				.getResultList();
	    	Pageable nextPage = pageable.next();
	    	boolean hasNext = found.size()==pageable.getPageSize(); // false se non ci sono altri elementi
	    	if (hasNext==true) {
	    		// Controlliamo se veramente ci sono altri elementi dopo
	    		@SuppressWarnings("unchecked")
				List<NewsJournal> nextElement = yadaSql.query(em)
	    			.setFirstResult((int) nextPage.getOffset())
	    			.setMaxResults(1)
	    			.getResultList();
	    		hasNext = !nextElement.isEmpty();
	    	}
	    
	    if (loadImageDescriptions) {
	    	YadaUtil.prefetchLocalizedStringListRecursive(found, NewsJournal.class);
	    }

		Slice<NewsJournal> result = new SliceImpl<>(found, nextPage, hasNext);
    	return result;
	}

	 /**
     * Trova la NewsJournal precedente o successivo
     * @param previous true per il previous, false per il next
     * @param project
     * @param pageable
     * @return
     */
	public Slice<NewsJournal> findWithLocalitasPreviousNext(boolean previous, NewsJournal newsJournal, Pageable pageable) {
		AmdYadaSql yadaSql = (AmdYadaSql) AmdYadaSql.instance().selectFrom("from NewsJournal n")
			.join("join fetch n.titlePartOne")
			.where("where n<>:newsJournal").and()
			.where("n.enabled = true").and()
			.where("n.unlisted = false").and()
			.where(previous, "(n.showDate > :showDate or (n.showDate =:showDate and n.id>:id))").and()
			.where(!previous, "(n.showDate < :showDate or (n.showDate =:showDate and n.id<:id))").and()
			//.limit(pageable.getPageSize())
			.setParameter("newsJournal", newsJournal)
			.setParameter("showDate", newsJournal.getShowDate())
			.setParameter("id", newsJournal.getId());
		yadaSql.orderBy(pageable);
		List<NewsJournal> result = yadaSql.query(em, NewsJournal.class).setFirstResult((int) pageable.getOffset()).getResultList();
		Slice<NewsJournal> resultPage = new PageImpl<>(result, pageable, 0);
		return resultPage;
	}

}
