package com.artemide.common.repository;

import java.io.File;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.hibernate.Hibernate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.data.domain.SliceImpl;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.artemide.common.persistence.entity.ProductFile;
import com.artemide.common.persistence.entity.ProductFileType;
import com.yr.entity.Articolo;
import com.yr.entity.Famiglia;
import com.yr.entity.Lampadina;
import com.yr.entity.Led;
import com.yr.entity.Prodotto;
import com.yr.entity.ProdottoElectrical;
import com.yr.entity.Subfamily;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Join;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import net.yadaframework.components.YadaFileManager;
import net.yadaframework.components.YadaWebUtil;
import net.yadaframework.persistence.YadaSql;
import net.yadaframework.persistence.entity.YadaAttachedFile;

@Repository
@Transactional(readOnly = true)
public class ProdottoDao {

	@PersistenceContext EntityManager em;
	
	@Autowired private ProductFileDao productFileDao;
	@Autowired private YadaFileManager yadaFileManager;
	@Autowired private MessageSource messageSource;

	// private int countDownloads = 0; // Not used

	public class FileAndName {
		public File file;
		public String name;
		public String attributeName;
	}

	public class FileTypeData {
		public String key;
		public String titolo;
		public String descrizione;
		public String url;
		public int tot=0;
	}

	public class AccessorioData {
		public Articolo articolo;
		public String imageUrl;
		public String descrizione;
		public String codiceSap;
		public int tot=0;
	}

	public class ProdottoElectricalData {
		public Map<String, String> valoriMap;
		public ProdottoElectrical prodottoElectrical;
		public String nome;
		public int tot=0;
	}

	public class LedData {
		public Led led;
		public String imageUrl;
		public int tot=0;
	}

	public class LampadinaData {
		public Lampadina lampadina;
		public String imageUrl;
		public int tot=0;
	}

	public class ProdottoData {
		public boolean published = false;
		public List<String> imagesUrls;
		public List<String> silhouetteImagesUrls;
//    	public List<String> installationImagesUrls;
		public List<String> homologationImagesUrls;

		public List<String[]> filesData; // Not used anymore
		public List<String[]>  instrucionsPdf;
		public List<String[]>  radiometricPdf;
		public List<String[]>  bimRevit;
		public Map<String,List<String[]>> filesType; // attribute name --> {titolo, description (empty), file url, clientFilename}
		public Map<String, String> fileTypeMap = makeFileTypeMap(); // attribute name --> section title key. E.g. "model2d" --> "productfilesection.title.MODEL2D"
		public Map<String, String> valoriMap;
		public String[] awardsArray;
		public Prodotto prodotto;
		public String slug;
		public String electricalName;

		public List<LampadinaData> lampadine;
		public List<LampadinaData> lampadineExcluded;
		public List<LampadinaData> lampadineExcludedAlt;

		public List<LedData> leds;
		public List<LedData> ledsExcluded;
		public List<LedData> ledsExcludedAlt;
		public List<ProdottoElectricalData> prodottoElectricals;

		public List<AccessorioData> accessori;
		// public int countDownloads; // Not used

		public String description;
		public String tipologiaDescription;
	}

	private String publishedQuery = "from Prodotto p where p.stato = com.yr.entity.Prodotto.STATUS_PRODOTTO_PUBLISHED and p.countryCode is null";
	
	/**
	 * // attribute name --> section title key. E.g. "model2d" --> "productfilesection.title.MODEL2D"
	 * @return
	 */
	private Map<String, String> makeFileTypeMap() {
		Map<String, String> result = new LinkedHashMap<>(); // LinkedHashMap keeps the insertion order
		for (ProductFileType productFileType : ProductFileType.values()) {
			if (productFileType != ProductFileType.GALLERY) {
				// Any locale is good as we only define the value for english that is the fallback
				result.put(productFileType.getType(), "productfilesection.title." + productFileType.name());
			}
		}
		return result;
	}

	/**
	 * Returns the timestamp to use in the footer of the product sheet pdf. It should be equal to the last modification time.
	 * @param prodottoId
	 * @return timestamp or null
	 */
	public Date getPdfTimestamp(Long prodottoId) {
		String sql = "select pdfTimestamp from ProdottoPdfTimestamp where prodotto.id=:id";
		List<Date> resultList = em.createQuery(sql, Date.class).setParameter("id", prodottoId).getResultList();
		if (resultList.isEmpty()) {
			return null;
		}
		return resultList.get(0);
	}
	
	/**
	 * Set the pdf timestamp to now
	 * @param prodottoId
	 */
	@Transactional(readOnly = false)
	public void setPdfTimestamp(Long prodottoId) {
		String sql = "update ProdottoPdfTimestamp p set p.pdfTimestamp = :timestamp where p.prodotto.id = :id";
		em.createQuery(sql)
		  .setParameter("timestamp", new Date())
		  .setParameter("id", prodottoId)
		  .executeUpdate();
	}

	/**
	 * Conta quanti prodotti devono essere inseriti nella sitemap.xml
	 * @return
	 */
	public Long countProductsForSitemap() {
		return em.createQuery("select count (stato) " + publishedQuery, Long.class).getSingleResult();
	}

	/**
	 * Ritorna i dati per la sitemap
	 * @param offset
	 * @param pageSize
	 * @return
	 */
	public List<EntityData> getProductDatasForSitemap(int offset, int pageSize) {
		List<EntityData> result = new ArrayList<>();
		List<Prodotto> found = YadaSql.instance().selectFrom(publishedQuery)
			.query(em, Prodotto.class)
			.setFirstResult(offset)
			.setMaxResults(pageSize)
			.getResultList();

			for (Prodotto prodotto : found) {
				Subfamily subfamily = prodotto.getSubfamily();
				EntityData prodottoData = new EntityData();
				prodottoData.id = prodotto.getId();
				prodottoData.name = prodotto.getName();
				prodottoData.subName = prodotto.getSubName();
				prodottoData.subfamilyId = subfamily.getId();
				prodottoData.subfamilyName = subfamily.getName();
				prodottoData.prodottoSlug = YadaWebUtil.makeSlugStatic(prodottoData.name);
				prodottoData.subfamilySlug = YadaWebUtil.makeSlugStatic(prodottoData.subfamilyName);
				result.add(prodottoData);
			}
			return result;
	}

	// Non usato
	public Slice<Prodotto> findProducts(Pageable pageable) {
		YadaSql yadaSql = YadaSql.instance().selectFrom("from Prodotto p")
				.where("where p.stato = com.yr.entity.Prodotto.STATUS_PRODOTTO_PUBLISHED").and()
				.where("p.countryCode is null").and()
				.orderBy("p.pos desc");
			List<Prodotto> found = yadaSql.query(em, Prodotto.class)
				.setFirstResult((int) pageable.getOffset())
				.setMaxResults(pageable.getPageSize())
				.getResultList();
			Pageable nextPage = pageable.next();
			boolean hasNext = found.size()==pageable.getPageSize(); // false se non ci sono altri elementi
			if (hasNext==true) {
				// Controlliamo se veramente ci sono altri elementi dopo
				List<Prodotto> nextElement = yadaSql.query(em, Prodotto.class)
					.setFirstResult((int) nextPage.getOffset())
					.setMaxResults(1)
					.getResultList();
				hasNext = !nextElement.isEmpty();
			}
			Slice<Prodotto> result = new SliceImpl<>(found, nextPage, hasNext);
			return result;
	}
	
	/**
	 * Returns all subfamily products, sorted by name
	 * @param subfamilyId
	 * @param locale needed for sorting by name
	 */
	public List<Prodotto> findAllSorted(Long subfamilyId, Locale locale) {
		String sql = "select p from Prodotto p join p.subfamily s join p.names n "
			+ "where s.id = :subfamilyId and n.localeCode = :localeCode order by n.value";
		return em.createQuery(sql, Prodotto.class)
			.setParameter("localeCode", locale.toString())
			.setParameter("subfamilyId", subfamilyId)
			.getResultList();
	}
	
	/**
	 * Returns all subfamily products, sorted by name, with components prefetched
	 * @param subfamilyId
	 * @param locale needed for sorting by name
	 */
	public List<Prodotto> findAllSortedWithComponents(Long subfamilyId, Locale locale) {
		String sql = "select p from Prodotto p join p.subfamily s join p.names n "
				+ "left join fetch p.componenti "
				+ "where s.id = :subfamilyId and n.localeCode = :localeCode order by n.value";
		return em.createQuery(sql, Prodotto.class)
				.setParameter("localeCode", locale.toString())
				.setParameter("subfamilyId", subfamilyId)
				.getResultList();
	}

	/**
	 * Ritorna tutti i prodotti del catalogo oppure della famiglia indicata oppure della sottofamiglia indicata
	 * @param series codice della serie (0, 1,...) oppure null
	 * @param familyId id della famiglia oppure null
	 * @param subFamilyId null oppure id della sottofamiglia
	 * @param published true per avere solo i prodotti published (anche famiglie e sottofamiglie lo devono essere)
	 */
	public List<Prodotto> findAllProducts(Integer seriesInteger, Long familyId, Long subFamilyId, boolean published) {
		if (subFamilyId!=null) {
			familyId = null;
			seriesInteger = null;
		} else if (familyId!=null) {
			seriesInteger=null;
		}
		String series = seriesInteger!=null?seriesInteger.toString():null;
		YadaSql yadaSql = YadaSql.instance().selectFrom("select p from Prodotto p")
			// .join("left join p.names n")
			.join("left join p.componenti c")
			.join("left join p.subfamily s")
			.join("left join s.famiglia f")
			//.orderBy("n")
			.where(subFamilyId!=null, "s.id = :subFamilyId").and()
			.where(familyId!=null, "f.id = :familyId").and()
			.where(published, "p.stato = :stato").and()
			.where("s.published = :published").and()
			.where("f.published = :published").and()
			.startSubexpression(series!=null)
				.where("s.series = :series").or()
				.where("s.series like CONCAT(:series, ',%')").or()
				.where("s.series like CONCAT('%,', :series)").or()
				.where("s.series like CONCAT('%,', :series, ',%')").or()
			.endSubexpression().and()
			.setParameter("series", series)
			//.where(locale!=null, "KEY(n)=:locale").and()
			//.setParameter("locale", locale.toString())
			.setParameter("stato", com.yr.entity.Prodotto.STATUS_PRODOTTO_PUBLISHED)
			.setParameter("published", published)
			.setParameter("subFamilyId", subFamilyId)
			.setParameter("familyId", familyId);
		List<Prodotto> result = yadaSql.query(em, Prodotto.class).getResultList();
		for (Prodotto prodotto : result) {
			// prodotto.getNames().size();
			prodotto.getComponenti().size();
		}
		return result;
	}

	public ProdottoData getProdottoData(Long id) {
		Prodotto prodotto = em.find(Prodotto.class,  id);
		ProdottoData prodottoData= new ProdottoData();
		prodottoData.published = prodotto.isPublished();
		prodottoData.imagesUrls= getImagesUrls(prodotto);
		prodottoData.silhouetteImagesUrls= getSilhouetteImagesUrls(prodotto);
//		prodottoData.installationImagesUrls= getInstallationImagesUrls(prodotto);
		prodottoData.homologationImagesUrls= getImageHomologation(prodotto);
//		prodottoData.filesData = getFilesData(prodotto); // Not used anymore
		prodottoData.filesType = getFilesTypeMap(prodotto);
//		prodottoData.instrucionsPdf = getInstructionsPdf(prodotto);
		// prodottoData.radiometricPdf = getRadiometricPdf(prodotto);
//		prodottoData.bimRevit = getBimRevit(prodotto);

		prodottoData.prodotto = prodotto;
		prodottoData.awardsArray = getAwardsArray(prodotto);
		prodottoData.valoriMap= getValoriMap(prodotto);
		prodottoData.accessori = makeAccessorioData(prodotto.getAccessori());

		prodottoData.lampadine = makeLampadinaData(prodotto.getListLamps());
		prodottoData.lampadineExcluded = makeLampadinaData(prodotto.getListLampsExcluded());
		prodottoData.lampadineExcludedAlt = makeLampadinaData(prodotto.getListLampsExcludedAlt());

		prodottoData.prodottoElectricals = makeProdottoElectricalData(prodotto.getProdottoElectricals());
		prodottoData.leds = makeLedData(prodotto.getListLeds());
		prodottoData.ledsExcluded = makeLedData(prodotto.getListLedsExcluded());
		prodottoData.ledsExcludedAlt = makeLedData(prodotto.getListLedsExcludedAlt());
		// prodottoData.countDownloads = countDownloads;
		prodottoData.slug = YadaWebUtil.makeSlugStatic(prodotto.getName());

		prodottoData.description = prodotto.getDescription();
		prodottoData.tipologiaDescription = prodotto.getTipologiaDescription();

		return prodottoData;
	}

// Al momento non richiesto
//    public String getSubNameForPdf(Long id) {
//    	Prodotto prodotto = em.find(Prodotto.class,  id);
//		return prodotto.getSubName();
//    }

	/**
	 * Ritorna i dati di un insieme di subfamily
	 * @param ids gli id dei prodotti
	 * @return
	 */
	public List<EntityData> getProductData(Collection<Long> ids) {
		List<EntityData> result = new ArrayList<>();
		if (ids.isEmpty()) {
			return result;
		}
		List<Prodotto> found = YadaSql.instance().selectFrom("from Prodotto where id in :ids")
			.orderBy("FIELD(id, :ids)") // Ordino in base all'ordine degli id passati
			.setParameter("ids", ids)
			.query(em, Prodotto.class).getResultList();
		for (Prodotto prodotto : found) {
			Subfamily subfamily = prodotto.getSubfamily();
			EntityData prodottoData = new EntityData();
			prodottoData.id = prodotto.getId();
			prodottoData.name = prodotto.getName();
			prodottoData.subName = prodotto.getSubName();
			prodottoData.imageUrl = prodotto.getProdottoImageUrl();
			prodottoData.galleryImageUrl = prodotto.getSubfamily().getOrderedGalleryImageUrls().get(0);
			prodottoData.subfamilyId = subfamily.getId();
			prodottoData.subfamilyName = subfamily.getName();
			prodottoData.prodottoSlug = YadaWebUtil.makeSlugStatic(prodottoData.name);
			prodottoData.subfamilySlug = YadaWebUtil.makeSlugStatic(prodottoData.subfamilyName);
			result.add(prodottoData);
		}
		return result;
	}

	/**
	 * Ritorna i dati della subfamily e del prodotto
	 * @param id del prodotto
	 * @return
	 */
	public EntityData getProductData(Long idProdotto) {
		if (idProdotto==null) {
			return null;
		}
		Prodotto prodotto = YadaSql.instance().selectFrom("from Prodotto where id=:idProdotto")
			//.orderBy("FIELD(id, :id)") // Ordino in base all'ordine degli id passati
			.setParameter("idProdotto", idProdotto)
			.query(em, Prodotto.class).getSingleResult();

		Subfamily subfamily = prodotto.getSubfamily();
		EntityData prodottoData = new EntityData();
		prodottoData.id = prodotto.getId();
		prodottoData.name = prodotto.getName();
		prodottoData.subName = prodotto.getSubName();
		prodottoData.imageUrl = prodotto.getProdottoImageUrl();
		prodottoData.galleryImageUrl = prodotto.getSubfamily().getOrderedGalleryImageUrls().get(0);
		prodottoData.subfamilyId = subfamily.getId();
		prodottoData.subfamilyName = subfamily.getName();
		prodottoData.prodottoSlug = YadaWebUtil.makeSlugStatic(prodottoData.name);
		prodottoData.subfamilySlug = YadaWebUtil.makeSlugStatic(prodottoData.subfamilyName);

		return prodottoData;
	}

	//ritorna il codice da inserire nel prodotto pdf
	//Il prodotto può essere null, ma ci deve essere almeno l'id del prodotto.
	public String getCodiceForPdf(Prodotto prodotto, Long idProdotto) {
		if (idProdotto==null) {
			return null;
		}

		if (prodotto==null) {
			prodotto = YadaSql.instance().selectFrom("select p from Prodotto p")
				.join("left join p.componenti c")
				.where("p.id = :idProdotto")
				.setParameter("idProdotto", idProdotto)
				.query(em, Prodotto.class).getSingleResult();
		}

		 String codice = "NEW";

		 if (!prodotto.isNewProduct()) {
			 if (!prodotto.isScenarios()) {
				 codice = prodotto.getCodiceSapProdotto();

				 if (!codice.isEmpty()) {
					 String[] listaCodice = codice.split(" ");
					 codice = listaCodice[0];
				 }
			 }else {
				 codice = "SCENARIOS";
			 }
		 }

		 return codice;
	}


	private List<AccessorioData> makeAccessorioData(List<Articolo> articoli) {
		Map<Long, AccessorioData> tmpMap = new HashMap<>();
		for (Articolo articolo : articoli) {
			Long id = articolo.getId();
			AccessorioData accessorioData = tmpMap.get(id);
			if (accessorioData==null) {
				accessorioData = new AccessorioData();
				accessorioData.articolo = articolo;
				accessorioData.imageUrl = articolo.getImageUrl();
				accessorioData.descrizione = articolo.getDescrizione();
				accessorioData.codiceSap = articolo.getCodiceSap();
				tmpMap.put(id, accessorioData);
			}
			accessorioData.tot++;
		}
		return new ArrayList<>(tmpMap.values());
	}

	private List<ProdottoElectricalData> makeProdottoElectricalData(List<ProdottoElectrical> prodottoElectricals) {
		Map<Long, ProdottoElectricalData> tmpMap = new HashMap<>();
		for (ProdottoElectrical prodottoElectrical : prodottoElectricals) {
			Long id = prodottoElectrical.getId();
			ProdottoElectricalData prodottoElectricalData = tmpMap.get(id);
			if (prodottoElectricalData==null) {
				prodottoElectricalData = new ProdottoElectricalData();
				prodottoElectricalData.prodottoElectrical = prodottoElectrical;
				prodottoElectricalData.nome = prodottoElectrical.getNome();
				prodottoElectricalData.valoriMap = getValoriElectricalMap(prodottoElectrical);
				tmpMap.put(id, prodottoElectricalData);
			}
			prodottoElectricalData.tot++;
		}
		return new ArrayList<>(tmpMap.values());
	}

	private List<LedData> makeLedData(List<Led> leds) {
		Map<Long, LedData> tmpMap = new HashMap<>();
		for (Led led : leds) {
			Long id = led.getId();
			LedData ledData = tmpMap.get(id);
			if (ledData==null) {
				ledData = new LedData();
				ledData.led = led;
				ledData.imageUrl = led.getLedImageUrl();
				tmpMap.put(id, ledData);
			}
			ledData.tot++;
		}
		return new ArrayList<>(tmpMap.values());
	}

	private List<LampadinaData> makeLampadinaData(List<Lampadina> lamps) {
		Map<Long, LampadinaData> tmpMap = new HashMap<>();
		for (Lampadina lampadina : lamps) {
			Long id = lampadina.getId();
			LampadinaData lampadinaData = tmpMap.get(id);
			if (lampadinaData==null) {
				lampadinaData = new LampadinaData();
				lampadinaData.lampadina = lampadina;
				lampadinaData.imageUrl = lampadina.getLampImageUrl();
				tmpMap.put(id, lampadinaData);
			}
			lampadinaData.tot++;
		}
		return new ArrayList<>(tmpMap.values());
	}

	/**
	 * Returns the sorted list of gallery urls
	 * @param prodotto
	 */
	private List<String> getImagesUrls(Prodotto prodotto) {
		List<YadaAttachedFile> gallery = productFileDao.getAttachedFiles(prodotto, ProductFileType.GALLERY);
		return gallery.stream().map(yadaFileManager::getFileUrl).toList();
		// Legacy version:
		// return prodotto.getSortedFileUrls(Prodotto.ATTR_IMAGE, Prodotto.KEY_RELATIVE_IMAGE_DIR, Prodotto.KEY_DIMENSION_PRODUCT_GALLERY);
	}

	/**
	 * Returns the sorted list of silhouette urls
	 * @param prodotto
	 */
	private List<String> getSilhouetteImagesUrls(Prodotto prodotto) {
		List<YadaAttachedFile> allSilhouettes = productFileDao.getAttachedFiles(prodotto, ProductFileType.SILHOUETTE);
		return allSilhouettes.stream().map(yadaFileManager::getFileUrl).toList();
		// Legacy version:
		// return prodotto.getSortedFileUrls(Prodotto.ATTRIBUTENAME_SILHOUETTE, Prodotto.KEY_RELATIVE_IMAGE_DIR, Prodotto.KEY_DIMENSION_PRODUCT_SILHOUETTE);
	}
//
//    private List<String> getInstallationImagesUrls (Prodotto prodotto) {
//    	return prodotto.getSortedFileUrls(Prodotto.ATTR_INSTALLATIONIMAGE, Prodotto.KEY_RELATIVE_IMAGE_DIR, Prodotto.KEY_DIMENSION_PRODUCT_INSTALLATION);
//    }

//    /**
//     * Ritorna soltanto il file delle istruzioni in pdf
//     * @param prodotto
//     * @return
//     */
//    public List<String[]> getInstructionsPdf(Prodotto prodotto) {
//    	return prodotto.getFileData(Prodotto.ATTRIBUTENAME_INSTRUCTIONS);
//    }

//    public List<String[]> getRadiometricPdf(Prodotto prodotto) {
//    	return prodotto.getFileData(Prodotto.ATTRIBUTENAME_RADIOMETRIC);
//    }

//    public List<String[]> getBimRevit(Prodotto prodotto) {
//    	return prodotto.getFileData(Prodotto.ATTRIBUTENAME_BIM);
//    }

//    private List<String[]> getFilesData (Prodotto prodotto) {
//    	return prodotto.getFileData(Prodotto.ATTRIBUTENAME_LDTIMAGE, Prodotto.KEY_DIMENSION_PRODUCT_LDTIMAGE);
//    }

	/**
	 * Returns a map where for each ProductFileType there's a list of arrays with [titolo, descrizione, url, id, clientFilename].
	 * Gallery images are not considered.
	 * @param prodotto
	 */
	private Map<String, List<String[]>> getFilesTypeMap(Prodotto prodotto) {
		//
		// Migrated to use YadaAttachedFile
		// There's no need to support the legacy version because all file types of the original FileTypeMap have been migrated
		@SuppressWarnings("unchecked")
		// The query automatically ignores gallery images because they have a null pf.attachedFile_id
		List<Object[]> data = YadaSql.instance().selectFrom("SELECT pf.type, yaf.id, yaf.filename, yaf.clientFilename, yaft.title, yaf.relativeFolderPath FROM ProductFile pf")
		.join("join ProductFile_Prodotto pfp on pf.id = pfp.productfile_id") 
		.join("join YadaAttachedFile yaf on yaf.id = pf.attachedFile_id") 
		.join("join YadaAttachedFile_title yaft on yaft.YadaAttachedFile_id = pf.attachedFile_id") 
		.where("where pfp.prodotto_id = :prodottoId").and()
		.where("locale=:locale")
		.setParameter("prodottoId", prodotto.getId())
		.setParameter("locale", LocaleContextHolder.getLocale())
		.nativeQuery(em)
		.getResultList();
		
		//Map<String, String> map = Prodotto.getFileTypeMap();
		Map<String, List<String[]>> result = new HashMap<>();
		for (Object[] row : data) {
			int column=0;
			Integer typeOrdinal = ((Number)row[column++]).intValue();
			String idString = ((Long) row[column++]).toString();
			String filename = (String) row[column++];
			String clientFilename = (String) row[column++];
			String localTitle = (String) row[column++];
			String relativeFolderPath = (String) row[column++];
			String attributeName = ProductFileType.fromOrdinal(typeOrdinal).getType();
			String url = yadaFileManager.getFileUrl(relativeFolderPath, filename);
			String[] toStore = new String[] {localTitle, "", url, idString, clientFilename};
			List<String[]> values = result.get(attributeName);
			if (values==null) {
				values = new ArrayList<>();
				result.put(attributeName, values);
			}
			values.add(toStore);
		}

		// Legacy version removed
		//    	// countDownloads = 0;
		//
		//    	for (String attributeName : map.keySet()) { // Ciclo su ogni categoria di file: 2D_MODEL, SILHOUETTE, etc
		//    		List<String[]> fileData = prodotto.getFileData(attributeName);
		//    		if (!fileData.isEmpty()) {
		//    			result.put(attributeName, fileData);
		//    			// countDownloads++;
		//    		}
		//		}
		return result;
	}

	/**
	 * Returns all product files with their title, including gallery images
	 * @param prodotto
	 */
	public List<FileAndName> getAllFilesWithName(Prodotto prodotto) {
		// TODO does it have to receive the Locale and return the localized title?
		//
		// Migrated to use YadaAttachedFile 
		List<FileAndName> result = new ArrayList<>();
		for (ProductFileType productFileType : ProductFileType.values()) { // Ciclo su ogni categoria di file: 2D_MODEL, SILHOUETTE, etc
			List<YadaAttachedFile> yadaAttachedFiles = productFileDao.getAttachedFiles(prodotto, productFileType);
			for (YadaAttachedFile yadaAttachedFile : yadaAttachedFiles) {
				// Not using the title anymore, I think the clientFilename is better
				// String name = yadaAttachedFile.getLocalTitle();
				String name = yadaAttachedFile.getClientFilename();
				// String fileId = yadaAttachedFile.getId().toString();
				//
				File file = yadaFileManager.getAbsoluteFile(yadaAttachedFile);
				FileAndName fileAndName = new FileAndName();
				fileAndName.name = name;
				fileAndName.file = file;
				fileAndName.attributeName = productFileType.getType();
				result.add(fileAndName);
			}
		}
		return result;
	}

// Not used anymore
//    /**
//     * Ritorna tutti i file associati a un prodotto
//     * @param prodotto
//     * @return
//     */
//    public List<File> getAllFiles(Prodotto prodotto) {
//    	Map<String, String> filesMap = Prodotto.getFileTypeMap();
//    	List<File> result = new ArrayList<>();
//
//    	for (String attributeName : filesMap.keySet()) { // Ciclo su ogni categoria di file: 2D_MODEL, SILHOUETTE, etc
//    		List<File> files = prodotto.getFileList(attributeName, null);
//    		result.addAll(files);
//    	}
//    	return result;
//    }

	private Map<String, String> getValoriMap (Prodotto prodotto) {
		//em.merge(prodotto);
		Map<String, String> map = new HashMap<>();
		map.put("name", prodotto.getName()); //th:text=${mappa.description}
		map.put("shortName", prodotto.getShortName());
		map.put("subName", prodotto.getSubName());
		map.put("electricalName", prodotto.getElectricalName());
		map.put("description", prodotto.getDescription());
		map.put("note", prodotto.getNote());
		map.put("color", prodotto.getColor());
		map.put("trasformer", prodotto.getTransformer());
		map.put("emergency", prodotto.getEmergency());
		map.put("remotecontrol", prodotto.getRemoteControl());
		map.put("basecolor", prodotto.getBaseColor());
		map.put("material", prodotto.getMaterial());
		//map.put("award", prodotto.getAward().split("_"));
		map.put("imageAward", getImageAward(prodotto));
		map.put("imageInsulation", getImageInsulation(prodotto));
		map.put("imageTunableWhite", getImageLedTunableWhite(prodotto));
		map.put("imageDimmable", getImageDimmable(prodotto));
		map.put("codiceSap", prodotto.getCodiceSapProdotto());
		map.put("ballast", prodotto.getBallast());
		return map;
	}

	private Map<String, String> getValoriElectricalMap (ProdottoElectrical prodottoElectrical) {
		//em.merge(prodotto);
		Map<String, String> map = new HashMap<>();
		map.put("name", prodottoElectrical.getNome());
		map.put("trasformer", prodottoElectrical.getTransformer());
		map.put("emergency", prodottoElectrical.getEmergency());
		map.put("remotecontrol", prodottoElectrical.getRemoteControl());
		map.put("ballast", prodottoElectrical.getBallast());
		return map;
	}

	private String[] getAwardsArray (Prodotto prodotto) {
		String premi=prodotto.getAward();
		String[] premiArray = null;
		if (!StringUtils.isEmpty(premi)) {
			premiArray=premi.split("_");
		}
		return premiArray;
	}

	private String getImageAward (Prodotto prodotto) {
		String premio = prodotto.getAward();
		String image = "";
		if (!StringUtils.isEmpty(premio)) {
			if (premio.toLowerCase().contains("red dot")) {
				image = "/res/images/scheda-prodotto/awards/reddots.gif";
			}
			if (premio.toLowerCase().contains("if design")) {
				image = "/res/images/scheda-prodotto/awards/pda.gif";
			}
			if (premio.toLowerCase().contains("compasso")) {
				image = "/res/images/scheda-prodotto/awards/compasso.jpg";
			}
		}
		return image;
	}

	private String getImageInsulation (Prodotto prodotto) {
		String insulationClass = prodotto.getInsulationClass();
		String image = "";

		if (!StringUtils.isEmpty(insulationClass)) {
			if (insulationClass.trim().equals("I")) {
				image = "/res/images/scheda-prodotto/light-spec-white/light-spec-insulation-class-I.svg";
			}
			if (insulationClass.trim().equals("II")) {
				image = "/res/images/scheda-prodotto/light-spec-white/light-spec-insulation-class-II.svg";
			}
			if (insulationClass.trim().equals("III")) {
				image = "/res/images/scheda-prodotto/light-spec-white/light-spec-insulation-class-III.svg";
			}
		}
		return image;
	}

	private String getImageLedTunableWhite (Prodotto prodotto) {
		boolean ledTunableWhite = prodotto.isLedTunableWhite();
		String image = "";
		if(ledTunableWhite) {
			image = "/res/images/tunablewhite-small.gif";
		}

		return image;
	}

	/*
	 * Restituisce una lista di immagini corrispondenti all'omologazione
	 */
	private List<String> getImageHomologation (Prodotto prodotto) {
		String homologation = prodotto.getHomologation();
		String image = "";
		List<String> listHomologations= new ArrayList<>();

		if (!StringUtils.isEmpty(homologation)) {
//	    	if (homologation.contains("IMQ")) {
//	    		image = "/res/images/scheda-prodotto/light-spec-white/light-spec-imq.gif";
//	    		listHomologations.add(image);
//	    	}
//	    	if (homologation.contains("F-mark")) {
//	    		image = "/res/images/scheda-prodotto/light-spec-white/light-spec-f-mark.gif";
//	    		listHomologations.add(image);
//	    	}
			if (homologation.contains("Enec15")) {
				//image = "/res/images/scheda-prodotto/light-spec-white/light-spec-enec15.gif";
				image = "/res/images/scheda-prodotto/light-spec-white/light-spec-enec.svg"; //perde il 15
				listHomologations.add(image);
			}
			if (homologation.contains("PCT")) {
				//image = "/res/images/scheda-prodotto/light-spec-white/light-spec-pct.gif"; //è stato sostituito
				image = "/res/images/scheda-prodotto/light-spec-white/light-spec-eac.svg";
				listHomologations.add(image);
			}
			if (homologation.contains("CCC")) {
				image = "/res/images/scheda-prodotto/light-spec-white/light-spec-ccc.svg";
				listHomologations.add(image);
			}
			if (homologation.contains("CE")) {
				image = "/res/images/scheda-prodotto/light-spec-white/light-spec-ce.svg";
				listHomologations.add(image);
			}
			if (homologation.contains("EPD")) {
				image = "/res/images/scheda-prodotto/homologation/EPD-Italy-grigio.svg";
				listHomologations.add(image);
			}
		}
		return listHomologations;
	}

	private String getImageDimmable (Prodotto prodotto) {
		boolean ledTunableWhite = prodotto.isDimmable();
		String image = "";
		if(ledTunableWhite) {
			image = "/res/images/scheda-prodotto/dimmer/light-dimmer.gif";
		}

		return image;
	}

//    public void test(Long subfamilyId, Locale locale) {
//    	List<String> colors = YadaSql.instance().selectFrom("select distinct c.value from Prodotto p")
//    		.join("join p.colors c")
//    		.where("where p.subfamily.id = :subfamilyId").and()
//    		.where("KEY(c) = :localeString").and()
//			.where(!AmdUtil.isPreview(), "p.stato = :published")
//			.setParameter("subfamilyId", subfamilyId)
//			.setParameter("localeString", locale.toString())
//			.setParameter("published", com.yr.entity.Prodotto.STATUS_PRODOTTO_PUBLISHED)
//    		.query(em, String.class)
//    		.getResultList();
//    	List<String> ccts = YadaSql.instance().selectFrom("select distinct p.CCT from Prodotto p")
//    			.where("where p.subfamily.id = :subfamilyId").and()
//    			.where(!AmdUtil.isPreview(), "p.stato = :published")
//    			.setParameter("subfamilyId", subfamilyId)
//    			.setParameter("published", com.yr.entity.Prodotto.STATUS_PRODOTTO_PUBLISHED)
//    			.query(em, String.class)
//    			.getResultList();
//
//
//
//    }



	public List<Prodotto> findProtuctsToExport(Long familyId, Long subfamilyId, Long Catalogue, int exportPublished) {
		CriteriaBuilder cb = em.getCriteriaBuilder();
		CriteriaQuery<Prodotto> cq = cb.createQuery(Prodotto.class);
		Root<Prodotto> prodotto = cq.from(Prodotto.class);
		List<Predicate> predicates = new ArrayList<>();

		if (subfamilyId != null && subfamilyId != -1) {
			Join<Prodotto, Subfamily> subfamily = prodotto.join("subfamily");
			predicates.add(cb.equal(subfamily.get("id"), subfamilyId));
		}

		if (Catalogue != null && Catalogue > -1) {
			Join<Prodotto, Subfamily> subfamily = prodotto.join("subfamily");
			String catalogueStr = Catalogue.toString();
			Predicate cataloguePredicate = cb.or(
					cb.equal(subfamily.get("series"), catalogueStr),
					cb.like(subfamily.get("series"), catalogueStr + ",%"),
					cb.like(subfamily.get("series"), "%," + catalogueStr),
					cb.like(subfamily.get("series"), "%," + catalogueStr + ",%")
			);
			predicates.add(cataloguePredicate);
		}

		if (familyId != null && familyId != -1) {
			Join<Prodotto, Subfamily> subfamily = prodotto.join("subfamily");
			Join<Subfamily, Famiglia> family = subfamily.join("famiglia");
			predicates.add(cb.equal(family.get("id"), familyId));
		}

		// Handling published flag
		if (exportPublished != -1) { // (not) Export all
			Join<Prodotto, Subfamily> subfamily = prodotto.join("subfamily");
			Join<Subfamily, Famiglia> family = subfamily.join("famiglia");
			if (exportPublished == 1) { // Export published
				predicates.add(cb.equal(subfamily.get("published"), true));
				predicates.add(cb.equal(family.get("published"), true));
				predicates.add(cb.equal(prodotto.get("stato"), Prodotto.STATUS_PRODOTTO_PUBLISHED));
			} else if (exportPublished == 0) { // Export not published
				predicates.add(cb.or(
						cb.equal(subfamily.get("published"), false),
						cb.equal(family.get("published"), false),
						cb.equal(prodotto.get("stato"), Prodotto.STATUS_PRODOTTO_UNPUBLISHED)
				));
			} else {
				predicates.add(cb.equal(prodotto.get("stato"), Prodotto.STATUS_PRODOTTO_DISABLED));
			}
		}

		cq.where(cb.and(predicates.toArray(new Predicate[0])));
		TypedQuery<Prodotto> query = em.createQuery(cq);

		List<Prodotto> list = query.getResultList();

		list.forEach(p -> {
			initLazyProdottoFields(p);
		});

		return list;
	}

	private static void initLazyProdottoFields(Prodotto p) {
		Hibernate.initialize(p.getDescriptions());
		Hibernate.initialize(p.getNotes());
		Hibernate.initialize(p.getElectricalNames());
		Hibernate.initialize(p.getBallasts());
		Hibernate.initialize(p.getBaseColors());
		Hibernate.initialize(p.getColors());
		Hibernate.initialize(p.getEmergencies());
		Hibernate.initialize(p.getMaterials());
		Hibernate.initialize(p.getRemoteControls());
		Hibernate.initialize(p.getTransformers());
		Hibernate.initialize(p.getmTipologia());
		Hibernate.initialize(p.getVariante());
		Hibernate.initialize(p.getListLamps());
		Hibernate.initialize(p.getListLeds());
		Hibernate.initialize(p.getListLampsExcluded());
		Hibernate.initialize(p.getListLedsExcluded());
		Hibernate.initialize(p.getListLampsExcludedAlt());
		Hibernate.initialize(p.getListLedsExcludedAlt());
		Hibernate.initialize(p.getAccessori());
		Hibernate.initialize(p.getComponenti());
		Hibernate.initialize(p.getProdottoElectricals());
		initLazyProdottoElectricalFields(p.getProdottoElectricals());
	}
	
    private static void initLazyProdottoElectricalFields(List<ProdottoElectrical> prodottoList) {    
        prodottoList.forEach(pe -> {
            Hibernate.initialize(pe.getNames());
            Hibernate.initialize(pe.getBallast());
            Hibernate.initialize(pe.getEmergencies());
            Hibernate.initialize(pe.getRemoteControls());
            Hibernate.initialize(pe.getTransformers());
        });   
    }

	public Prodotto getProdotto(Long id) {
		YadaSql yadaSql = YadaSql.instance().selectFrom("select p from Prodotto p").where("p.id = :id")
				.setParameter("id", id);
		Prodotto prodotto = yadaSql.query(em, Prodotto.class).getSingleResult();
		initLazyProdottoFields(prodotto);
		return prodotto;
	}
	
	public Prodotto find(Long id) {
		return em.find(Prodotto.class, id);
	}

	/**
	 * Find all products that have the given article either in the components or in the accessories
	 * @param sapCode the article SAP code
	 */
	@SuppressWarnings("unchecked")
	public List<Prodotto> findProductsWithSapCode(String sapCode) {
		return YadaSql.instance().selectFrom("select DISTINCT p.* from Prodotto p")
			.join("LEFT JOIN Prodotto_Accessorio pa on p.id = pa.Prodotto_id")
			.join("LEFT JOIN Prodotto_Componente pc on p.id = pc.Prodotto_id")
			.join("LEFT JOIN Articolo a1 ON pa.accessori_id = a1.id")
			.join("LEFT JOIN Articolo a2 ON pc.componenti_id = a2.id")
			.where("WHERE a1.codiceSap = :sapCode").or()
			.where("WHERE a2.codiceSap = :sapCode").or()
			.setParameter("sapCode", sapCode)
			.nativeQuery(em, Prodotto.class)
			.getResultList();
	}

	/**
	 * Find all products that have any of the given articles either in the components or in the accessories
	 * @param sapCodes the list of article SAP codes
	 */
	@SuppressWarnings("unchecked")
	public List<Prodotto> findProductsWithSapCodes(String[] sapCodes) {
		return YadaSql.instance().selectFrom("select DISTINCT p.* from Prodotto p")
				.join("LEFT JOIN Prodotto_Accessorio pa on p.id = pa.Prodotto_id")
				.join("LEFT JOIN Prodotto_Componente pc on p.id = pc.Prodotto_id")
				.join("LEFT JOIN Articolo a1 ON pa.accessori_id = a1.id")
				.join("LEFT JOIN Articolo a2 ON pc.componenti_id = a2.id")
				.where("WHERE a1.codiceSap IN (:sapCodes)").or()
				.where("WHERE a2.codiceSap IN (:sapCodes)")
				.setParameter("sapCodes", sapCodes)
				.nativeQuery(em, Prodotto.class)
				.getResultList();
	}
	
//	public List<ProdottoElectrical> getProdottoElectricals(Prodotto prodotto) {
//		prodotto = em.merge(prodotto);
//		List<ProdottoElectrical> prodottoElectricals = prodotto.getProdottoElectricals();
//		prodottoElectricals.size(); // Load the collection
//		return prodottoElectricals;
//	}
	
    public List<ProdottoElectrical> getProdottoElectricals(Prodotto prodotto) {
        List<ProdottoElectrical> prodottoList = em
                .createQuery("SELECT pe FROM Prodotto p JOIN p.prodottoElectricals pe WHERE p = :prodotto",
                        ProdottoElectrical.class)
                .setParameter("prodotto", prodotto).getResultList();
        initLazyProdottoElectricalFields(prodottoList);        
        return prodottoList;
    }
	
}
