﻿.modal-header{align-items:flex-start}.btn-secondary{background-color:#fff;color:#000}.invalid-feedback{margin:-5px 0 10px 0}modal-content.card,.resetPassword .card{border-radius:.3rem;width:460px}modal-content.card-header,.resetPassword .card-header{display:flex;flex-shrink:0;align-items:center;justify-content:space-between;padding:1rem 1rem;margin:0;background:none;border:0}modal-content.card-body,.resetPassword .card-body{padding-left:60px;padding-right:60px}modal-content.card-title,.resetPassword .card-title{text-align:center;text-align:center;font-weight:600;margin:0 auto}modal-content.card-footer,.resetPassword .card-footer{padding:1rem 1rem;background:none;border:0}.invalid-feedback{color:red}.invalid-feedback a,.invalid-feedback a:hover,.invalid-feedback a:visited{color:red !important}.form-group{margin-bottom:15px}a[data-bs-toggle=collapse]{text-decoration:none}html{font-size:16px}header,footer{background-color:#f1f1f1;padding-top:10px}footer{margin-top:50px;height:50px}main{min-height:300px}hr{margin-top:50px;margin-bottom:10px}.nowrap{white-space:nowrap}.description{font-size:.9rem}#userTable tr.userProfileAdmin td:not(.yadaCommandButtonCell){color:red}.yadaRowCommandButton:not(.disabled) i{color:#000}.yadaRowCommandButton.disabled i{color:#ccc}.yadaImpersonationBanner{position:fixed;top:0;left:0;right:0;padding:0 5px;height:13px;z-index:9999;background-color:#ff0;font-size:12px;text-align:center}.yadaImpersonationBanner a,.yadaImpersonationBanner a:hover,.yadaImpersonationBanner a:visited{color:#000 !important;position:relative;top:-3px;text-decoration:none}.yadaImpersonationBanner a button,.yadaImpersonationBanner a:hover button,.yadaImpersonationBanner a:visited button{float:right}.yadaIcon-edit:before{content:""}.yadaIcon-down:before{content:""}.yadaIcon-up:before{content:""}.yadaIcon-edit:before{content:""}.yadaIcon-delete:before{content:""}.yadaIcon-clone:before{content:""}.yadaIcon-add:before{content:""}.yadaIcon-eye:before{content:""}/*# sourceMappingURL=yex.min.css.map */
