plugins {
	id 'java-library'
	id 'org.hidetake.ssh' version '2.11.2'
	id 'maven-publish'
	id 'signing'
}

apply from: '../YadaWeb/loadSharedProperties.gradle'

group = 'net.yadaframework'
version = yada_version

apply plugin: 'groovy'
// apply plugin: 'java-gradle-plugin'
apply plugin: 'eclipse'

if (!project.hasProperty('repoPath')) {
	ext.repoPath="repoPathMissing"
}

java {
    sourceCompatibility = JavaVersion.valueOf("VERSION_${java_version}")
    targetCompatibility = JavaVersion.valueOf("VERSION_${java_version}")
     // toolchain {
     //     languageVersion = JavaLanguageVersion.of(java_version) // >=11 because of org.hidetake.ssh
     // }
	// https://docs.gradle.org/6.0/userguide/java_plugin.html#sec:java-extension
	// withSourcesJar()
    // withJavadocJar() // https://stackoverflow.com/a/75710366/587641
}

eclipse {
	jdt {
		// sourceCompatibility = 1.8
		// targetCompatibility = 1.8
		// https://stackoverflow.com/a/35302104/587641
		file {
      		File dir = file('.settings')
      		dir.mkdirs()
      		File f = file('.settings/org.eclipse.core.resources.prefs')
      		if (!f.exists()) {
      			f.write('eclipse.preferences.version=1\n')
      			f.append('encoding/<project>=utf-8')
      		}
    	}
	}
}

repositories {
	mavenCentral()
}

dependencies {
	implementation gradleApi(), localGroovy(),
		'org.hibernate.javax.persistence:hibernate-jpa-2.1-api:1.0.2.Final' 
}

/* TODO Da finire: lanciando da eclipse la console è nulla. Vedi http://stackoverflow.com/a/24360969/587641 
        Usa il codice esistente per la richiesta conferma */
task terraforming {
	doLast {
	    def hostname, serverip, rootPassword;
	    def projectBasePath;
	    def console = System.console()
		hostname = console.readLine('> Please enter the non-qualified host name (e.g. saturn): ')
		serverip = console.readLine('> Please enter the server ip address: ')
		rootPassword = console.readLine('> Please enter the root password: ')
		projectBasePath = console.readLine('> Please enter the project base path (e.g. /srv/ldmprod: ')
		remotes.create('targetHost') {
		    host = serverip
		    user = root
		    password = rootPassword
		}
		ssh.run {
			session(remotes.targetHost) {
			  put from: "./scripts/defaultCourtesyPage", into: projectBasePath+"/contents"
			}
		}
		/* TODO finire caricando i file ed eseguendoli
			println "Running deploy script..."
			ssh.run {
				session(remotes.deployHost) {
					execute """sudo bash -O extglob -c 'sudo rm -rf ${tomcatbase}/webapps/ROOT/WEB-INF/!(lib)' &&
						sudo -u ${config.server.tomcatuser} unzip -o /$remoteDest/$filename -d ${tomcatbase}/webapps/ROOT &&
						/srv/${acroenv}/bin/restart.sh"""
				}
			}
			delete "$distsDir/$filename"
			println "Deploy of ${filename} done."
		} else {
			println "Aborted."
		}
		*/
	}
}

javadoc {
    options.encoding = 'UTF-8'
    options.docEncoding = 'UTF-8'
    options.charSet = 'UTF-8'
    failOnError = false
}

task sourcesJar(type: Jar) {
    archiveClassifier = 'sources'
    from sourceSets.main.allJava
}

task javadocJar(type: Jar) {
    archiveClassifier = 'javadoc'
    from javadoc.destinationDir
}

publishing {
    publications {
        yadaToolsLibrary(MavenPublication) {
        	artifactId = 'yadatools'
            from components.java

            artifact sourcesJar
            artifact javadocJar
			pom {
			    name = 'YadaTools'
			    description = 'Some useful tasks for the Yada Framework'
			    url = 'https://yadaframework.net/en/index.html'
			    inceptionYear = '2014'
			    packaging = 'jar'
			    licenses {
			        license {
			            name = 'MIT License'
			            url = 'https://en.wikipedia.org/wiki/MIT_License'
			        }
			    }
			    developers {
			        developer {
			            id = 'xtianus'
			            name = 'Studio Ghezzi'
			            email = '<EMAIL>'
			        }
			    }
			    organization {
			    	name = 'Studio Ghezzi'
			    	url = 'https://studio.ghezzi.net/'
			    }
			    scm {
			        connection = 'scm:git:**************:xtianus/yadaframework.git'
			        developerConnection = 'scm:git:**************:xtianus/yadaframework.git'
			        url = 'https://github.com/xtianus/yadaframework'
			    }
			}
        }
    }
    repositories {
    	// Creates task publishYadaToolsLibraryPublicationToLocalRepoRepository
        maven {
        	name "PublicRepo"
            def snapshotsRepoUrl = "https://oss.sonatype.org/content/repositories/snapshots/"
            def releasesRepoUrl = "https://oss.sonatype.org/service/local/staging/deploy/maven2/"
            url = version.endsWith('SNAPSHOT') ? snapshotsRepoUrl : releasesRepoUrl
            credentials(PasswordCredentials) {
            	username = project.hasProperty('ossrhUsername')?ossrhUsername:'USERUNSET'
            	password = project.hasProperty('ossrhPassword')?ossrhPassword:'PWDUNSET'
            }
        }
        // Creates task publishYadaToolsLibraryPublicationToLocalRepoRepository
        maven {
        	name "LocalRepo"
        	// repoPath must be passed when invoking gradle
        	url = "file://${repoPath}"
    	}
    }    
}

tasks.getByName("publishYadaToolsLibraryPublicationToLocalRepoRepository") { 
	it.dependsOn ':YadaWeb:publishYadaWebLibraryPublicationToLocalRepoRepository',
		":YadaWebCMS:publishYadaWebCMSLibraryPublicationToLocalRepoRepository",
		":YadaWebCommerce:publishYadaWebCommerceLibraryPublicationToLocalRepoRepository",
		":YadaWebSecurity:publishYadaWebSecurityLibraryPublicationToLocalRepoRepository"
}

tasks.getByName("publishYadaToolsLibraryPublicationToPublicRepoRepository") { 
	it.dependsOn ':YadaWeb:publishYadaWebLibraryPublicationToPublicRepoRepository',
		":YadaWebCMS:publishYadaWebCMSLibraryPublicationToPublicRepoRepository",
		":YadaWebCommerce:publishYadaWebCommerceLibraryPublicationToPublicRepoRepository",
		":YadaWebSecurity:publishYadaWebSecurityLibraryPublicationToPublicRepoRepository"
}

signing {
    sign publishing.publications.yadaToolsLibrary
}
